import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { Color, FillPaints, FillType, useFill } from '@vis/document-core';
import { ButtonGroup } from '../../../../models';
import VisFillGradient from './gradient/index.vue';
import VisFillImage from './image/index.vue';

/**
 * 填充
 * 用于字体颜色、背景填充
 * 支持纯色(solid)、图片(image)、线性渐变(gradient_linear)、径向渐变(gradient_radial)、旋转渐变(gradient_angular)、菱形渐变(gradient_diamond)
 * 支持仅设置颜色模式（onlyColor）
 * 支持极简模式，仅显示一个色块
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-fill',
  components: { VisFillGradient, VisFillImage },
  props: {
    /**
     * 填充值
     * onlyColor为true时传入Color值，其余情况传入FillPaints
     */
    modelValue: {
      type: Object as PropType<FillPaints | Color>,
      required: true
    },
    /** 是否只显示纯色 */
    onlyColor: {
      type: Boolean,
      default: false
    },
    /** 显示标题 */
    showTitle: {
      type: Boolean,
      default: false
    },
    minusWidth: {
      type: Number,
      default: 28
    },
    /**
     * 眼睛是否显示
     */
    showEyes: {
      type: Boolean,
      default: true
    },
    /**
     * 极简模式
     */
    mini: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const { getFillStyle, savePaletteColor, getRgbaFromString, hexToRgba } = useFill();

    const computedModel = computed({
      get() {
        if (!props.modelValue) {
          return new FillPaints();
        }

        if (props.onlyColor) {
          if ((props.modelValue as FillPaints).color) {
            return props.modelValue as FillPaints;
          } else {
            const fillPaints = new FillPaints();
            fillPaints.color = props.modelValue as Color;
            return fillPaints;
          }
        } else {
          return props.modelValue as FillPaints;
        }
      },

      set(value) {
        Object.assign(props.modelValue, value);
      }
    });

    // 引入纯色颜色值
    const computedColor = computed({
      get() {
        return computedModel.value.color;
      },
      set(value) {
        Object.assign(computedModel.value.color, value);
      }
    });

    const colorType = ref(props.onlyColor ? FillType.Solid : computedModel.value.type);

    const isSolid = computed(() => {
      return colorType.value === FillType.Solid;
    });
    const isGradient = computed(() => {
      return colorType.value.includes('gradient');
    });
    const isImage = computed(() => {
      return colorType.value === FillType.Image;
    });
    const colorTypeUpdate = (type: string) => {
      colorType.value = type as FillType;
      handleUpdate(Object.assign({}, props.modelValue, { type: type }));

      emit('changeType', type);
    };

    const handleUpdate = (val: FillPaints | Color) => {
      emit('update:modelValue', val);
    };

    const solidRef = ref();
    const gradientRef = ref();

    /**
     * 弹窗隐藏时记录选择的颜色
     */
    const handleHide = () => {
      if (isSolid.value) {
        savePaletteColor(colorType.value, solidRef.value?.colorValue);
      } else if (isGradient.value) {
        const color = {
          stops: gradientRef.value?.stops,
          rotation: gradientRef.value?.rotation
        };
        savePaletteColor(colorType.value, color);
      }
    };

    const updateValue = () => {
      const { r, g, b } = getRgbaFromString(colorValue.value);
      const a = Number(alpha.value.replace('%', '')) / 100;

      if (props.onlyColor) {
        handleUpdate(Object.assign({}, props.modelValue, { r, g, b, a }));
      } else {
        handleUpdate(Object.assign({}, props.modelValue, { color: { r, g, b, a }, opacity: a }));
      }
      emit('change');
    };

    // #region 透明度
    const alpha = ref('');
    const oldAlpha = ref('');

    const alphaRef = ref();
    const focusAlpha = () => {
      oldAlpha.value = alpha.value;
      alphaRef.value && alphaRef.value.select();
    };

    /**
     * 保持alpha在0-100之间
     */
    const updateAlpha = () => {
      const alphaNum = Number(alpha.value.replace('%', ''));
      if (!alpha.value || Number.isNaN(alphaNum)) {
        alpha.value = oldAlpha.value;
      } else {
        if (alphaNum < 0) {
          alpha.value = '0%';
        } else if (alphaNum > 100) {
          alpha.value = '100%';
        } else {
          alpha.value = `${alphaNum}%`;
        }
        oldAlpha.value = alpha.value;

        updateValue();
      }
    };
    // #endregion

    // #region 颜色值
    const oldColor = ref('');
    const colorRef = ref();
    const colorValue = ref('');

    /**
     * 初始化颜色和透明度
     */
    const initColor = () => {
      const { r, g, b, a } = computedModel.value.color;
      colorValue.value = `rgba(${r},${g},${b},${a})`;

      const opacity = isSolid.value ? a : computedModel.value.opacity || 1;
      alpha.value = `${(opacity * 100).toFixed(0)}%`;
    };
    watch(
      () => computedModel.value,
      () => {
        initColor();
      },
      { immediate: true, deep: true }
    );

    // 计算 hex 值
    const hexColor = computed(() => {
      const { r, g, b } = computedModel.value.color;
      // 确保数值在 0-255 范围内
      const clamp = (val: number) => Math.max(0, Math.min(255, val));
      const hex = [r, g, b]
        .map(clamp)
        .map((x) => x.toString(16).padStart(2, '0'))
        .join('');
      return `${hex}`.toUpperCase();
    });

    const focusColor = () => {
      oldColor.value = colorValue.value;
      colorRef.value && colorRef.value.select();
    };

    /**
     * rgb参数合法
     * @param r
     * @param g
     * @param b
     * @returns
     */
    const isValidRgb = (r: number, g: number, b: number) => {
      return (
        Number.isInteger(r) &&
        r >= 0 &&
        r <= 255 &&
        Number.isInteger(g) &&
        g >= 0 &&
        g <= 255 &&
        Number.isInteger(b) &&
        b >= 0 &&
        b <= 255
      );
    };

    /**
     * 改变输入框值时，面板中的颜色值跟随变化
     * @param ev
     * @returns
     */
    const updateColor = (ev?: Event) => {
      const targetValue = (ev?.target as HTMLInputElement)?.value;
      if (targetValue) {
        colorValue.value = targetValue;
      }

      if (!colorValue.value) return;

      // 不是rgb开头则认为是hex模式
      if (!colorValue.value.startsWith('rgb')) {
        colorValue.value = hexToRgba(colorValue.value);
      }

      const { r, g, b, a } = getRgbaFromString(colorValue.value);
      if (!colorValue.value || !isValidRgb(r, g, b)) {
        colorValue.value = oldColor.value;
      } else {
        colorValue.value = `rgb(${r}, ${g}, ${b})`;
        oldColor.value = colorValue.value;
        alpha.value = `${(a * 100).toFixed(0)}%`;

        updateValue();
      }
    };

    watch(() => props.modelValue, initColor, { deep: true });
    // #endregion

    // #region 渐变

    const typeOptions: ButtonGroup[] = [
      {
        label: '',
        value: FillType.Solid,
        icon: 'solid',
        tip: '纯色'
      },
      {
        label: '',
        value: FillType.Linear,
        icon: 'linear',
        tip: '线性渐变'
      },
      {
        label: '',
        value: FillType.Radial,
        icon: 'radial',
        tip: '径向渐变'
      },
      {
        label: '',
        value: FillType.Angular,
        icon: 'angular',
        tip: '旋转渐变'
      },
      {
        label: '',
        value: FillType.Diamond,
        icon: 'diamond',
        tip: '菱形渐变'
      },
      {
        label: '',
        value: FillType.Image,
        icon: 'image',
        tip: '图片'
      }
      // {
      //   label: '',
      //   value: 'video',
      //   icon: 'hticon-vis-video',
      //   tip: '视频'
      // }
    ];

    const fillTypeName = computed(() => {
      if (!colorType.value) return '';
      switch (colorType.value) {
        case FillType.Linear:
          return '线性渐变';
        case FillType.Radial:
          return '径向渐变';
        case FillType.Angular:
          return '旋转渐变';
        case FillType.Diamond:
          return '菱形渐变';
        case FillType.Image:
          return '图片';
        default:
          return '';
      }
    });

    const isEmptyImage = computed(() => {
      return isImage.value && !computedModel.value.image?.url;
    });

    /**
     * 计算输入框内的图标样式
     */
    const iconStyle = computed(() => {
      if (!colorType.value) return '';

      const type = props.onlyColor ? { type: FillType.Solid } : {};
      const fillStyle = getFillStyle({ ...computedModel.value, ...type, visible: true, opacity: 1 });

      // 设置图标透明，防止遮挡背景
      fillStyle.color = isEmptyImage.value ? '' : 'transparent';

      return fillStyle;
    });

    const visible = ref(computedModel.value.visible);
    const handleVisible = () => {
      visible.value = !visible.value;
      handleUpdate(Object.assign({}, props.modelValue, { visible: visible.value }));

      emit('toggle', visible.value);
    };
    // #endregion

    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      popupRef.value?.handleShow(e);
    };

    return {
      computedModel,
      computedColor,
      colorType,
      colorTypeUpdate,
      isSolid,
      isImage,
      isGradient,

      handleUpdate,

      handleHide,
      solidRef,
      gradientRef,

      alpha,
      alphaRef,
      focusAlpha,
      updateAlpha,

      colorValue,
      colorRef,
      focusColor,
      updateColor,
      hexColor,

      typeOptions,
      fillTypeName,
      iconStyle,
      isEmptyImage,

      visible,
      handleVisible,

      popupRef,
      popupShow,
      showPopup
    };
  }
});
