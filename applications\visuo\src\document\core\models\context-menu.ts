/** 鼠标位置 */
export interface MenuPosition {
  x: number;
  y: number;
}

/** 操作项 */
type ActionKey = string;
/** 操作组 */
type MenuItem = [string, ContextMenuType];
/** 菜单项 = 操作项 | 操作组 */
type MenuGroup = (ActionKey | MenuItem)[];
/** 菜单数据 */
export type ContextMenuType = MenuGroup[];

/** 菜单匹配项 */
export interface ContextMenuOptions {
  x: number;
  y: number;
  menus: ContextMenuType;
  node?: any;
}

/** 菜单实例 */
export interface ContextMenuInstance {
  show: (options: ContextMenuOptions) => void;
  hide: () => void;
}
