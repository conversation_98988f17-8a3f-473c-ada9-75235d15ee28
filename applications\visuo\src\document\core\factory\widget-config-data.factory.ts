import { AxisField, WidgetType } from '../models';

/**
 * 组件配置数据
 * <AUTHOR>
 */
export class WidgetConfigDataFactory {
  /**
   * 段落
   */
  get [WidgetType.Paragraph]() {
    return {
      datasetType: 'static',
      datasetId: '',
      matchs: {
        content: {
          name: '内容',
          field: []
        }
      }
    };
  }

  /**
   * 标题
   */
  get [WidgetType.Title]() {
    return {
      datasetType: 'static',
      datasetId: '',
      matchs: {
        title: {
          name: '标题',
          field: []
        }
      }
    };
  }

  /**
   * 选项卡
   */
  get [WidgetType.Tab]() {
    return {
      datasetType: 'static',
      datasetId: 'STATIC_DATA',
      matchs: {
        text: {
          name: '标签',
          field: [new AxisField('L', '类别', '类别', '', 'dimension', undefined, 'string')]
        },
        value: {
          name: '值',
          field: [new AxisField('P', '数量', '数量', '', 'dimension', undefined, 'string')]
        },
        disabled: {
          name: '禁用',
          field: []
        }
      }
    };
  }

  /**
   * 文本输入
   */
  get [WidgetType.Input]() {
    return {
      datasetType: 'static',
      datasetId: '',
      matchs: {
        input: {
          name: '文本输入',
          field: []
        }
      }
    };
  }

  /**
   * 获取组件配置
   * @param widgetType 组件类型
   * @returns 组件配置
   */
  get(widgetType: WidgetType) {
    return this[widgetType];
  }
}
