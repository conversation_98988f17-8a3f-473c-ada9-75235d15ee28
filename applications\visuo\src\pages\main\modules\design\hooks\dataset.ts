import type { <PERSON>setField } from '@hetu/metadata-shared';
import { FieldType } from '@hetu/platform-app';
import { AxisField, DocumentService, useDocumentStore } from '@vis/document-core';

/**
 * 数据集相关
 * <AUTHOR>
 */
export const useDataset = () => {
  const { staticDatas } = useDocumentStore();

  /**
   * 根据字段的数据类型返回图标
   * @param type
   */
  const getDataTypeIcon = (type?: string) => {
    switch (type) {
      case 'date':
      case 'date_1':
      case 'date_2':
      case 'datetime':
      case 'datetime_1':
      case 'datetime_2':
      case 'time':
      case 'time_1':
        return 'date';
      case 'integer':
      case 'number':
      case 'double':
        return 'integer';
      case 'string':
        return 'string';
      case 'area_province':
      case 'area_city':
      case 'area_district':
      case 'area':
        return 'string';
      default:
        return 'string';
    }
  };

  /**
   * 获取字段类型
   * @param value
   * @returns
   */
  const getFieldType = (value: string | number | Date) => {
    const typeObj = {
      dataType: 'string',
      type: FieldType.Dim
    };
    // 检查是否为数字
    if (typeof value === 'number' || (!isNaN(Number(value)) && value !== '')) {
      typeObj.dataType = 'number';
      typeObj.type = FieldType.Measure;
    }
    // 检查是否为日期
    else if (value instanceof Date || (typeof value === 'string' && !isNaN(new Date(value).getTime()))) {
      typeObj.dataType = 'date';
      typeObj.type = FieldType.Dim;
    }

    return typeObj;
  };

  /**
   * 获取静态数据字段列表
   * @param name
   */
  const getStaticDataField = async (name: string) => {
    const allStaticData = { ...(await DocumentService.loadStaticData()), ...staticDatas.value };
    const fieldData = allStaticData[name][0] || {};
    const data = allStaticData[name][1] || {};
    const fields = Object.keys(fieldData);
    return fields.map((item) => {
      const typeObj = getFieldType(data[item] || '');
      return {
        id: item,
        fieldName: fieldData[item],
        fieldAlias: fieldData[item],
        fieldDatatype: typeObj.dataType,
        fieldType: typeObj.type
      } as DatasetField;
    });
  };

  /**
   * 获取静态数据
   * @param name
   */
  const getStaticData = (name: string) => {
    return staticDatas.value[name];
  };

  return {
    getDataTypeIcon,
    getStaticDataField,
    getStaticData
  };
};
