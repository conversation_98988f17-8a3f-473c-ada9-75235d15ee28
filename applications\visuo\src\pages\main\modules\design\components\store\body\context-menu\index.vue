<template>
  <teleport to="body">
    <div
      v-if="visible"
      ref="menuRef"
      class="vis-store-context-menu vis-menu"
      :style="menuStyle"
      @click.stop
      @contextmenu.prevent.stop
    >
      <div class="vis-store-context-menu-content">
        <q-scroll-area class="" :style="{ height: `${scrollHeight}px`, maxHeight: `${scrollMaxHeight}px` }">
          <q-list dense>
            <!-- <q-item-label header class="!text-xs !p-2 font-bold">操作</q-item-label> -->
            <template v-for="(group, index) in menu" :key="index">
              <template v-if="Array.isArray(group)">
                <q-item v-for="action in group" :key="action.name" clickable v-ripple @click="onActionClick(action)">
                  <q-item-section>{{ action.title }}</q-item-section>
                  <q-item-section side>
                    <label>
                      <span v-for="(keyboard, index) in action.shortcuts" :key="index">
                        <span v-if="index > 0">&nbsp;+</span>
                        {{ keyboard }}
                      </span>
                    </label>
                  </q-item-section>
                </q-item>
              </template>
              <template v-else>
                <q-item clickable v-ripple @click="onActionClick(group)">
                  <q-item-section>{{ group.title }}</q-item-section>
                  <q-item-section side>
                    <label>
                      <span v-for="(keyboard, index) in group.shortcuts" :key="index">
                        <span v-if="index > 0">&nbsp;+</span>
                        {{ keyboard }}
                      </span>
                    </label>
                  </q-item-section>
                </q-item>
              </template>
              <q-separator :key="index" v-if="index + 1 !== menu.length" class="!my-1" />
            </template>
          </q-list>
        </q-scroll-area>
      </div>
    </div>
  </teleport>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
