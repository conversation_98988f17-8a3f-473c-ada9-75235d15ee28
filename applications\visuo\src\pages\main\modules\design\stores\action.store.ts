import { ref } from 'vue';
import { defineStore } from '@hetu/core';
import { DesignAction, DesignActionGroup } from '../models';

/**
 * 工具栏操作
 * <AUTHOR>
 */
export const useActionStore = defineStore(() => {
  const actions = ref<Record<string, DesignAction>>(
    Object.assign(
      {},
      // dock 工具栏
      new DesignAction('move', '相交选中', [], 'o_near_me', true, 'mouse').record,
      new DesignAction('hand', '平移画布', [], 'o_back_hand', false, 'mouse').record,
      new DesignAction('scale', '等比缩放', [], 'o_photo_size_select_small', false, 'mouse').record,
      new DesignAction('frame', '容器', [], 'hticon-vis-frame ', false, 'mouse').record,
      new DesignAction('textbox', '文本', [], 'hticon-vis-textbox', false, 'mouse').record,
      new DesignAction('design', '设计模式', [], 'hticon-vis-mode-d', true, 'mode').record,
      new DesignAction('dhyana', '禅模式', [], 'hticon-vis-mode-s', false, 'mode').record,
      new DesignAction('node', '节点编程模式', [], 'o_cable', false, 'mode').record,
      new DesignAction('interact', '互动模式', [], 'o_touch_app', false, 'mode').record,
      new DesignAction('ai', 'AI', [], '').record,
      // page 页面
      new DesignAction('addToPageGroup', '添加到页面组', [], 'o_settings', false, 'page', false).record,
      new DesignAction('deletePage', '删除页面', [], 'o_settings', false, 'page', false).record,
      new DesignAction('copyPage', '复制页面', ['Ctrl', 'C'], 'o_settings', false, 'page', false).record,
      new DesignAction('renamePage', '重命名', [], 'o_settings', false, 'page', false).record,
      new DesignAction('setHomePage', '设为主页', [], 'o_settings', false, 'page', false).record,
      // new DesignAction('addIcon', '添加图标', [], 'o_settings', false, 'page', false).record,
      // page-group 页面组
      new DesignAction('cancelPageGroup', '取消页面组', [], 'o_settings', false, 'page-group', false).record,
      new DesignAction('deletePageGroup', '删除页面组', [], 'o_settings', false, 'page-group', false).record,
      new DesignAction('renamePageGroup', '重命名页面组', [], 'o_settings', false, 'page-group', false).record,
      // new DesignAction('addIcon', '添加图标', [], 'o_settings', false, 'page-group', false).record,
      // layer 图层
      new DesignAction('renameLayer', '重命名', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('copyLayer', '复制', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('pasteLayer', '粘贴', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('pasteAndReplaceLayer', '粘贴并替换', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('showOrHideLayer', '显示/隐藏图层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('lockOrUnlockLayer', '锁定/解锁图层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('moveUpLayer', '上移一层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('moveDownLayer', '下移一层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('moveTopLayer', '移到顶层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('moveBottomLayer', '移到底层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('copyComponentId', '复制组件ID', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('copyComponentConfig', '复制组件配置', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('createFrame', '创建容器', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('createGroup', '创建编组', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('cancelFrameOrGroup', '取消容器/编组', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('createWidget', '创建组件', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('editWidget', '编辑组件', [], 'o_settings', false, 'layer', false).record
    )
  );

  const groups = ref<Record<string, DesignActionGroup>>(
    Object.assign(
      {},
      new DesignActionGroup('page', '页面', 'o_settings').record,
      new DesignActionGroup('page-group', '页面组', 'o_settings').record,
      new DesignActionGroup('layer', '图层', 'o_settings').record
    )
  );

  // #region 构造菜单分组数据
  const getGroupMenu = (sorts: (string | any[])[], actions: Record<string, DesignAction | DesignActionGroup>) => {
    const getGroupMenuInner = (menuSorts: (string | any[])[]): (DesignAction | DesignActionGroup)[] => {
      return menuSorts
        .map((menu) => (Array.isArray(menu) ? getGroupMenuInner(menu) : actions[menu]))
        .filter(Boolean) as (DesignAction | DesignActionGroup)[];
    };
    return getGroupMenuInner(sorts);
  };

  // meta + ctrl键是否按下
  const isMetaCtrl = ref(false);

  return {
    actions,
    getGroupMenu,

    isMetaCtrl
  };
});
