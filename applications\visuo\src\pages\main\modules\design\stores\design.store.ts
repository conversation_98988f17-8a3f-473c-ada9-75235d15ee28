import { ref } from 'vue';
import { defineStore } from '@hetu/core';
import type InfiniteViewer from 'infinite-viewer';
import type Selecto from 'selecto';
import type Moveable from 'vue3-moveable';
import { syncedStore, getYjsDoc } from '@syncedstore/core';
import { WebsocketProvider } from 'y-websocket';
import { Page, Frame, type GuidesInterface, Graph } from '@vis/document-core';
import type { AggregatorFunction } from '@hetu/metadata-shared';
import type { Records } from '@hetu/util';
import { CanvasState, DesignActiveState, FrameState, RulerState } from '../models';
import type { MoveableManagerState } from 'vue3-moveable';

/**
 * 设计器状态管理
 * <AUTHOR>
 */
export const useDesignStore = defineStore(() => {
  const active = ref(new DesignActiveState());

  const fileStore = syncedStore({ file: {} as any });

  const fileYdoc = getYjsDoc(fileStore);

  // const wsProvider = new WebsocketProvider('ws://localhost:1234', 'fileDesign1a', fileYdoc);

  // 标尺
  const horizontalGuidesRef = ref<GuidesInterface>();
  const verticalGuidesRef = ref<GuidesInterface>();

  const rulerState = ref(new RulerState());

  // 无限画布
  const infiniteCanvasRef = ref<InfiniteViewer>();

  const moveableRef = ref<Moveable>();
  const moveableState = ref<MoveableManagerState>();
  const moveableTargets = ref<Array<HTMLElement>>([]);

  const selectoRef = ref<Selecto>();

  /** 画布状态 */
  const canvasState = ref(new CanvasState());

  //左侧选中的菜单
  const selectedMenu = ref<string>('pages');

  // 计数器
  const counter = ref(0);

  //#region 设计器状态
  // 左右两侧宽度值，默认240px
  const leftWidth = ref(240);
  const rightWidth = ref(240);
  // 设计器模式 vis 可视化 node 节点编程 analysis 分析
  const mode = ref('vis');

  //#endregion

  const aggregatorFunctions = ref<AggregatorFunction[]>([]);

  const frameState = ref<Records<FrameState>>({});

  const setFrameState = (id: string, key: string, value: any) => {
    if (!frameState.value[id]) {
      frameState.value[id] = new FrameState();
    }
    (frameState.value[id] as any)[key] = value;
  };

  const reset = () => {
    active.value = new DesignActiveState();
    rulerState.value = new RulerState();
    canvasState.value = new CanvasState();
    frameState.value = {};
    moveableTargets.value = [];
  };

  return {
    active,

    rulerState,
    horizontalGuidesRef,
    verticalGuidesRef,

    infiniteCanvasRef,

    moveableTargets,
    moveableRef,
    moveableState,

    selectoRef,

    canvasState,

    fileStore,
    fileYdoc,

    selectedMenu,

    counter,

    /** 左侧宽度 */
    leftWidth,
    /** 右侧宽度 */
    rightWidth,
    /** 设计器模式 */
    mode,

    /** 聚合函数列表 */
    aggregatorFunctions,

    frameState,
    setFrameState,

    reset
  };
});
