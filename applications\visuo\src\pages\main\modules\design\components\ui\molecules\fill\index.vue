<template>
  <div class="vis-fill h-full">
    <div v-if="mini" class="vis-fill--mini">
      <q-btn class="vis-field--mini" @click.stop="showPopup">
        <q-icon
          class="cursor-pointer vis-fill__icon"
          :class="isEmptyImage ? 'hticon-vis-image' : 'hticon-vis-rect'"
          style="font-size: 20px !important"
          :style="iconStyle"
        ></q-icon>
      </q-btn>
    </div>
    <div v-else class="vis-form-inline">
      <div :class="`vis-form-inline__content--minus-${minusWidth}`">
        <div class="flex vis-fill__content" :class="{ 'vis-invisible': !visible }">
          <!-- 颜色按钮 -->
          <q-btn class="vis-field--mini" @click.stop="showPopup">
            <q-icon
              class="cursor-pointer vis-fill__icon"
              :class="isEmptyImage ? 'hticon-vis-image' : 'hticon-vis-rect'"
              :style="iconStyle"
            ></q-icon>
          </q-btn>

          <!-- 输入框 -->
          <div class="flex-1 row items-center pl-1">
            <template v-if="isSolid">
              <q-input
                ref="colorRef"
                borderless
                dense
                :model-value="hexColor"
                @focus="focusColor"
                @keypress.enter="updateColor"
                class="vis-field--mini w-[calc(100%-41px)]"
              />
              <q-separator vertical inset class="!m-0" />
              <q-input
                borderless
                dense
                v-model="alpha"
                @focus="focusAlpha"
                @keypress.enter="updateAlpha"
                @blur="updateAlpha"
                class="vis-field--mini w-32px mx-1"
              />
            </template>
            <template v-else-if="isGradient">
              <q-input
                borderless
                dense
                :modelValue="fillTypeName"
                readonly
                class="vis-field--mini w-[calc(100%-41px)]"
              />
              <q-separator vertical inset class="!m-0" />
              <q-input
                ref="alphaRef"
                borderless
                dense
                v-model="alpha"
                @focus="focusAlpha"
                @keypress.enter="updateAlpha"
                @blur="updateAlpha"
                class="vis-field--mini w-32px mx-1"
              />
            </template>

            <q-input v-else borderless :modelValue="fillTypeName" readonly class="vis-field--mini col-12" />
          </div>
        </div>
      </div>

      <!-- 显隐按钮 -->
      <q-btn v-if="showEyes && !onlyColor" class="vis-field--mini btn-field" flat @click="handleVisible">
        <ht-icon class="vis-icon" :name="visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
      </q-btn>
    </div>

    <!-- 弹窗 -->
    <vis-popup ref="popupRef" :title="onlyColor ? '颜色' : '填充'" @before-hide="handleHide" :target="false">
      <div v-if="!onlyColor" class="vis-fill__types q-mb-sm vis-button-group flex-1 flex rounded-borders">
        <q-btn
          v-for="type in typeOptions"
          :key="type.value"
          flat
          dense
          @click="colorTypeUpdate(type.value)"
          class="flex-1"
          :class="{ 'vis-btn-active': colorType === type.value }"
        >
          <img
            class="img-icon"
            :src="`./static-next/svg/fill/fill-${type.icon}.svg`"
            :class="{ grayscale: colorType !== type.value }"
          />
          <q-tooltip :offset="[0, 4]"> {{ type.tip }} </q-tooltip>
        </q-btn>
      </div>

      <!-- 纯色 -->
      <vis-fill-color v-if="isSolid" ref="solidRef" v-model="computedColor" />

      <!-- 渐变 -->
      <vis-fill-gradient
        v-if="isGradient"
        ref="gradientRef"
        v-model="computedModel"
        @update:modelValue="handleUpdate"
      />

      <!-- 图片 -->
      <vis-fill-image v-if="isImage" v-model="computedModel" @update:modelValue="handleUpdate" />
    </vis-popup>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
