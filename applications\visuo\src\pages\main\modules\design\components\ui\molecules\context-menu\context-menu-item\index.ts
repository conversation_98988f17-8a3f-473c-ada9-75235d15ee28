import { defineComponent, computed, ref } from 'vue';
import type { PropType } from 'vue';
import type { MenuPosition, ContextMenuType, ContextMenuOptions } from '@vis/document-core';
import { useActionStore } from '../../../../../stores';

export default defineComponent({
  name: 'vis-context-menu-item',
  components: {},
  props: {
    menus: {
      type: Array as PropType<ContextMenuType>,
      required: true
    },
    depth: {
      type: Number,
      default: 0
    }
  },
  setup(props) {
    const actionStore = useActionStore();
    const actions = actionStore.actions;

    const getAction = (key: string) => {
      return actions.value[key];
    };

    // #region 滚动条高度和最大高度计算
    const SAFE_MARGIN = 10; // 安全边距
    const MAX_HEIGHT = 700; // 最大高度
    const scrollMaxHeight = ref<number>(MAX_HEIGHT);

    /**
     * 计算滚动区域高度
     * @returns number
     * */
    // const scrollHeight = computed(() => {
    //   // padding: 8/8; item: content 24 margin 4/4; separator: content 1 margin 4/4
    //   // 计算定高：8 + 28 * (item.length - 1 - separator.length) + 5 * (separator.length - 1) + 4 + 8
    //   const initialHeight = 8 + 8 + 4;
    //   console.log(props.menus, 'Array.isArray(cur)');
    //   const traverse = (menus: ContextMenuType): number => {
    //     return menus.reduce((pre, cur, index, arr) => {
    //       return pre + (Array.isArray(cur) ? traverse(cur) + 5 : 28);
    //     }, 0);
    //   };
    //   return traverse(props.menus) + initialHeight;
    //   // return props.menu.reduce((pre, cur) => {
    //   //   return pre + (cur.name === 'separator' ? 5 : 28);
    //   // }, initialHeight);
    // });

    return {
      getAction
    };
  }
});
