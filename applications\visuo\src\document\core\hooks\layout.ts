import { useDocumentStore } from '../stores';
import { DirectionType, HoriConsType, ResizeType, VertConsType, type Frame, type Graph } from '../models';
import type { Records } from '@hetu/util';
import { isNumber } from 'lodash-es';

/**
 * 图形布局
 * @returns
 */
export const useLayout = () => {
  const docStore = useDocumentStore();

  const isFreeform = (frame: Frame) => frame.autoLayout.direction === DirectionType.Freeform;

  const isFlex = (frame: Frame) =>
    [DirectionType.Horizontal, DirectionType.Vertical].includes(frame.autoLayout.direction);

  const isGrid = (frame: Frame) => frame.autoLayout.direction === DirectionType.Grid;

  /**
   * 自由布局样式
   * @param graph
   */
  const freeformStyle = (graph: Graph) => {
    const { width, height } = graph;
    const x = graph.transform.translate[0];
    const y = graph.transform.translate[1];
    const transform = `translate(${x}px, ${y}px) rotate(${graph.transform.rotate}deg)`;

    return {
      position: 'absolute',
      width: `${width}px`,
      height: `${height}px`,
      transform: transform,
      transformOrigin: graph.transform.origin
    };
  };

  /**
   * flex布局时的frame样式
   * @param graph
   * @param parent
   * @returns
   */
  const frameFlexStyle = (frame: Frame) => {
    const x = frame.transform.translate[0];
    const y = frame.transform.translate[1];
    const transform = `translate(${x}px, ${y}px) rotate(${frame.transform.rotate}deg)`;

    let style: Records<string | number> = {};

    // 父容器配置了flex布局，flex布局的样式在frame.ts里处理
    if (frame.autoLayout) {
      // flex布局和grid布局在wrap上处理，这里不需要设置宽高
      if (!isFreeform(frame)) {
        style = {
          position: 'absolute',
          width: `auto`,
          height: `auto`,
          transform: transform,
          transformOrigin: frame.transform.origin
        };
      }
    }
    return style;
  };

  /**
   * flex布局下的子元素样式
   * @param graph
   * @param parent
   * @returns
   */
  const flexItemStyle = (graph: Graph, parent: Frame) => {
    const { width, height } = graph;

    let itemStyle: Records<string | number> = {};

    // 父容器配置了自动布局后，处理子元素的样式
    if (parent.autoLayout) {
      const { direction } = parent.autoLayout;
      if (!isFreeform(parent) && !graph.ignoreAutoLayout) {
        itemStyle = {
          position: 'relative',
          order: graph.order
        };

        // 子元素： 固定尺寸 / 充满容器
        switch (graph.limitSize.width.resize) {
          case ResizeType.Fixed:
            itemStyle.width = `${width}px`;
            break;
          case ResizeType.Fill: {
            if (direction === DirectionType.Horizontal) {
              // 水平时，宽：占用剩余宽度
              itemStyle.flex = 1;
            } else {
              // 垂直时，宽：充满整个容器
              itemStyle.alignSelf = 'stretch';
            }
          }
        }

        switch (graph.limitSize.height.resize) {
          case ResizeType.Fixed:
            itemStyle.height = `${height}px`;
            break;
          case ResizeType.Fill:
            if (direction === DirectionType.Horizontal) {
              // 水平时，高度充满
              itemStyle.alignSelf = 'stretch';
            } else {
              // 垂直时，高度占剩余宽度
              itemStyle.flex = 1;
            }
        }

        // 最大最小限制
        if (isNumber(graph.limitSize.width.max)) {
          itemStyle.maxWidth = `${graph.limitSize.width.max}px`;
        }
        if (isNumber(graph.limitSize.width.min)) {
          itemStyle.minWidth = `${graph.limitSize.width.min}px`;
        }
        if (isNumber(graph.limitSize.height.max)) {
          itemStyle.maxHeight = `${graph.limitSize.height.max}px`;
        }
        if (isNumber(graph.limitSize.height.min)) {
          itemStyle.minHeight = `${graph.limitSize.height.min}px`;
        }
      }
    }
    return itemStyle;
  };

  /**
   * 返回frame wrap的样式， flex、grid在此元素上
   * @param frame
   * @returns
   */
  const frameWrapStyle = (frame: Frame) => {
    const { width, height } = frame;

    // wrap处理滚动条
    const style: Records<string | number> = {
      position: 'absolute',
      width: '100%',
      height: '100%',
      overflow: frame.clip ? 'hidden' : ''
    };

    const {
      direction,
      flowWarp,
      mainAxisAlignment,
      crossAxisAlignment,
      horizontalGap,
      verticalGap,
      padding,
      gridRowsSizing,
      gridColumnsSizing
    } = frame.autoLayout;

    let layoutStyle: Records<string | number> = {};

    // flex布局
    if (isFlex(frame)) {
      const isH = direction === DirectionType.Horizontal;
      layoutStyle = {
        position: 'relative',
        display: 'flex',
        flexDirection: isH ? 'row' : 'column',
        justifyContent: isH ? mainAxisAlignment : crossAxisAlignment,
        alignItems: isH ? crossAxisAlignment : mainAxisAlignment,
        flexWrap: 'nowrap',
        gap: direction === DirectionType.Horizontal ? `${horizontalGap}px` : `${verticalGap}px`,
        padding: padding.join('px ') + 'px'
      };

      // 水平布局换行
      if (flowWarp) {
        layoutStyle.flexWrap = 'wrap';
        layoutStyle.alignContent = 'start';
      }

      // 父级：固定尺寸 / 适应内容
      switch (frame.limitSize.width.resize) {
        case ResizeType.Fixed:
          layoutStyle.width = `${width}px`;
          break;
        case ResizeType.Adapt:
          layoutStyle.width = 'auto';
      }

      switch (frame.limitSize.height.resize) {
        case ResizeType.Fixed:
          layoutStyle.height = `${height}px`;
          break;
        case ResizeType.Adapt:
          layoutStyle.height = 'auto';
      }
    } else if (isGrid(frame)) {
      const templateRows = gridRowsSizing.map((size) => (isNaN(parseInt(`${size}`)) ? 'minmax(0, 1fr)' : `${size}px`));
      const templateColumns = gridColumnsSizing.map((size) =>
        isNaN(parseInt(`${size}`)) ? 'minmax(0, 1fr)' : `${size}px`
      );
      layoutStyle = {
        display: 'grid',
        gridTemplateRows: templateRows.join(' '),
        gridTemplateColumns: templateColumns.join(' '),
        columnGap: `${horizontalGap}px`,
        rowGap: `${verticalGap}px`,
        padding: padding.join('px ') + 'px'
      };
    }
    return { ...style, ...layoutStyle };
  };

  /**
   * grid布局时的frame样式
   * @param frame
   */
  const frameGridStyle = (frame: Frame) => {
    const { width, height } = frame;
    const x = frame.transform.translate[0];
    const y = frame.transform.translate[1];
    const transform = `translate(${x}px, ${y}px) rotate(${frame.transform.rotate}deg)`;

    let style: Records<string | number> = {};

    // 父容器配置了flex布局，flex布局的样式在frame.ts里处理
    if (frame.autoLayout) {
      // flex布局和grid布局在wrap上处理，这里不需要设置宽高
      style = {
        position: 'absolute',
        width: `${width}px`,
        height: `${height}px`,
        transform: transform,
        transformOrigin: frame.transform.origin
      };
    }
    return style;
  };

  /**
   * grid布局下的子元素样式
   * @param graph
   * @param parent
   * @returns
   */
  const gridItemStyle = (graph: Graph, parent: Frame) => {
    const { width, height } = graph;

    let itemStyle: Records<string | number> = {};

    // 父容器配置了自动布局后，处理子元素的样式
    if (parent.autoLayout && graph.gridItem) {
      const { rows, columns, justifySelf, alignSelf } = graph.gridItem;
      itemStyle = {
        position: 'relative',
        order: graph.order,
        gridRowStart: rows[0],
        gridRowEnd: rows[1],
        gridColumnStart: columns[0],
        gridColumnEnd: columns[1]
      };

      // 子元素： 固定尺寸 / 充满容器
      switch (graph.limitSize.width.resize) {
        case ResizeType.Fixed:
          itemStyle.width = `${width}px`;
          itemStyle.justifySelf = justifySelf;
          break;
        case ResizeType.Fill:
          break;
      }

      switch (graph.limitSize.height.resize) {
        case ResizeType.Fixed:
          itemStyle.height = `${height}px`;
          itemStyle.alignSelf = alignSelf;
          break;
        case ResizeType.Fill:
          break;
      }

      // 最大最小限制
      if (isNumber(graph.limitSize.width.max)) {
        itemStyle.maxWidth = `${graph.limitSize.width.max}px`;
      }
      if (isNumber(graph.limitSize.width.min)) {
        itemStyle.minWidth = `${graph.limitSize.width.min}px`;
      }
      if (isNumber(graph.limitSize.height.max)) {
        itemStyle.maxHeight = `${graph.limitSize.height.max}px`;
      }
      if (isNumber(graph.limitSize.height.min)) {
        itemStyle.minHeight = `${graph.limitSize.height.min}px`;
      }
    }
    return itemStyle;
  };

  /**
   * 返回配置约束图形的样式
   */
  const constraintsStyle = (graph: Graph, parent?: Frame) => {
    const style: Records = {
      position: 'absolute'
    };
    if (!graph.constraints || !parent) {
      return style;
    }

    const { width, height } = graph;
    const x = graph.transform.translate[0];
    const y = graph.transform.translate[1];

    const pW = parent.width;
    const pH = parent.height;

    const ch = graph.constraints.horizontal;
    const cv = graph.constraints.vertical;

    // 靠左固定: 宽度、左侧距离不变，右侧距离变化
    if (ch === HoriConsType.Left) {
      style.left = `${x}px`;
      style.width = `${width}px`;
    }

    // 靠右固定: 宽度、右侧距离不变，左侧距离变化
    if (ch === HoriConsType.Right) {
      style.right = `${pW - width - x}px`;
      style.width = `${width}px`;
      console.log(x, style.right);
    }
    // 水平左右固定：左侧、右侧不变,宽度变化
    if (ch === HoriConsType.Stretch) {
      style.left = `${x}px`;
      style.right = `${pW - width - x}px`;
    }
    // 水平居中：宽度不变，左右按比例变化
    if (ch === HoriConsType.Center) {
      style.width = `${width}px`;
      const centerX = (pW - width) / 2;
      style.left = `calc(50% - ${width}px / 2 + ${x - centerX}px)`;
    }
    // 水平跟随缩放：宽度、左右都按比例变化
    if (ch === HoriConsType.Scale) {
      style.left = `${(x / pW) * 100}%`;
      style.right = `${((pW - width - x) / pW) * 100}%`;
    }

    // 垂直靠上固定：高度、上侧距离不变，下侧变化
    if (cv === VertConsType.Top) {
      style.top = `${y}px`;
      style.height = `${height}px`;
    }

    // 垂直靠下固定：高度、下侧距离不变, 上侧变化
    if (cv === VertConsType.Bottom) {
      style.bottom = `${pH - height - y}px`;
      style.height = `${height}px`;
    }
    // 垂直上下固定：上侧、下侧不变的值,高度变化
    if (cv === VertConsType.Stretch) {
      style.top = `${y}px`;
      style.bottom = `${pH - height - y}px`;
    }
    // 垂直居中，高度不变，上下按比例变化
    if (cv === VertConsType.Center) {
      style.height = `${height}px`;
      style.top = `calc(50% - ${height}px / 2)`;
    }
    // 垂直居中，高度不变，上下按比例变化
    if (cv === VertConsType.Center) {
      style.height = `${height}px`;
      const centerY = (pH - height) / 2;
      style.top = `calc(50% - ${height}px / 2 + ${y - centerY}px)`;
    }
    // 垂直跟随缩放：高度、上下都按比例变化
    if (cv === VertConsType.Scale) {
      style.top = `${(y / pH) * 100}%`;
      style.bottom = `${((pH - height - y) / pH) * 100}%`;
    }

    return style;
  };

  return {
    isFreeform,
    isFlex,
    isGrid,
    freeformStyle,
    frameFlexStyle,
    flexItemStyle,
    frameGridStyle,
    frameWrapStyle,
    gridItemStyle,
    constraintsStyle
  };
};

// const { getStrokeStyle, getEffectStyle, createStyleVar } = useUiStyle();
//   const { getFillPaintsStyle } = useFill();

//   const getStyle = (graph: Graph, parent?: Frame) => {
//     const style: Records<string | number> = {
//       zIndex: graph.order,
//       borderRadius: graph.radius.join('px ') + 'px',
//       opacity: graph.opacity / 100
//     };

//     const { width, height } = graph;
//     const x = graph.transform.translate[0];
//     const y = graph.transform.translate[1];
//     const transform = `translate(${x}px, ${y}px) rotate(${graph.transform.rotate}deg)`;

//     let positionStyle: Records<string | number> = {
//       position: 'absolute',
//       width: `${width}px`,
//       height: `${height}px`,
//       transform: transform,
//       transformOrigin: graph.transform.origin
//     };

//     // 约束布局，并且不是左上固定
//     if (
//       graph.constraints &&
//       (graph.constraints.horizontal !== HoriConsType.Left || graph.constraints.vertical !== VertConsType.Top)
//     ) {
//       positionStyle = constraintsStyle(graph, parent);
//     } else if (parent && (parent as Frame).autoLayout) {
//       // flex布局
//       positionStyle = getFlexStyle(graph as Frame, parent);
//     }

//     // 主容器 不设置transform
//     if (graph.id === mainFrame.value.id) {
//       delete positionStyle.transform;
//       delete positionStyle.transformOrigin;
//     }

//     // 顶级容器，设置固定布局和响应式布局
//     if (graph.type === GraphType.Frame && !graph.parent) {
//       if ((graph as Frame).autoLayout.type === 'responsive') {
//         positionStyle.width = '100%';
//         positionStyle.height = '100%';
//         delete positionStyle.position;
//       } else {
//         positionStyle.width = `${width}px`;
//         positionStyle.height = `${height}px`;
//       }
//     }

//     // 计算填充、描边和特效
//     const fill = getFillPaintsStyle(graph.fillPaints);
//     const stroke = getStrokeStyle(graph.stroke as Stroke);
//     const effects = getEffectStyle(graph.effects as Effects);
//     const varStyle = createStyleVar({ ...fill, ...stroke, ...effects });

//     return { ...style, ...positionStyle, ...varStyle };
//   };

//   /**
//    * 处理图形上配置的flex布局，及flex布局的下级组件样式
//    * @param graph
//    * @param parent
//    * @returns
//    */
//   const getFlexStyle = (graph: Graph, parent?: Frame) => {
//     const { width, height } = graph;
//     const x = graph.transform.translate[0];
//     const y = graph.transform.translate[1];
//     const transform = `translate(${x}px, ${y}px) rotate(${graph.transform.rotate}deg)`;

//     let layoutStyle: Records<string | number> = {
//       position: 'absolute',
//       width: `${width}px`,
//       height: `${height}px`,
//       transform: transform,
//       transformOrigin: graph.transform.origin
//     };

//     // 父容器配置了flex布局，flex布局的样式在frame.ts里处理
//     if ((graph as Frame).autoLayout) {
//       const { direction } = (graph as Frame).autoLayout;
//       // flex布局和grid布局在wrap上处理，这里不需要设置宽高
//       if (direction !== DirectionType.Freeform) {
//         layoutStyle = {
//           position: 'absolute',
//           width: `auto`,
//           height: `auto`,
//           transform: transform,
//           transformOrigin: graph.transform.origin
//         };
//       }
//     }

//     // 父容器配置了自动布局后，处理子元素的样式
//     if (parent && (parent as Frame).autoLayout) {
//       const { direction } = (parent as Frame).autoLayout;
//       if (direction !== DirectionType.Freeform && !graph.ignoreAutoLayout) {
//         layoutStyle = {
//           position: 'relative',
//           order: graph.order
//         };

//         // 子元素： 固定尺寸 / 充满容器
//         switch (graph.limitSize.width.resize) {
//           case ResizeType.Fixed:
//             layoutStyle.width = `${width}px`;
//             break;
//           case ResizeType.Fill: {
//             if (direction === DirectionType.Horizontal) {
//               // 水平时，宽：占用剩余宽度
//               layoutStyle.flex = 1;
//             } else {
//               // 垂直时，宽：充满整个容器
//               layoutStyle.alignSelf = 'stretch';
//             }
//           }
//         }

//         switch (graph.limitSize.height.resize) {
//           case ResizeType.Fixed:
//             layoutStyle.height = `${height}px`;
//             break;
//           case ResizeType.Fill:
//             if (direction === DirectionType.Horizontal) {
//               // 水平时，高度充满
//               layoutStyle.alignSelf = 'stretch';
//             } else {
//               // 垂直时，高度占剩余宽度
//               layoutStyle.flex = 1;
//             }
//         }

//         // 最大最小限制
//         if (isNumber(graph.limitSize.width.max)) {
//           layoutStyle.maxWidth = `${graph.limitSize.width.max}px`;
//         }
//         if (isNumber(graph.limitSize.width.min)) {
//           layoutStyle.minWidth = `${graph.limitSize.width.min}px`;
//         }
//         if (isNumber(graph.limitSize.height.max)) {
//           layoutStyle.maxHeight = `${graph.limitSize.height.max}px`;
//         }
//         if (isNumber(graph.limitSize.height.min)) {
//           layoutStyle.minHeight = `${graph.limitSize.height.min}px`;
//         }
//       }
//     }
//     return layoutStyle;
//   };

//   /**
//    * 返回frame wrap的样式
//    * @param frame
//    * @returns
//    */
//   const getWrapStyle = (frame: Frame) => {
//     const { width, height } = frame;

//     // wrap处理滚动条
//     const style: Records<string | number> = {
//       position: 'absolute',
//       width: '100%',
//       height: '100%',
//       overflow: frame.clip ? 'hidden' : ''
//     };

//     const { direction, flowWarp, mainAxisAlignment, crossAxisAlignment, horizontalGap, verticalGap, padding } =
//       frame.autoLayout;

//     let layoutStyle: Records<string | number> = {};

//     // flex布局
//     if ([DirectionType.Horizontal, DirectionType.Vertical].includes(direction)) {
//       const isH = direction === DirectionType.Horizontal;
//       layoutStyle = {
//         position: 'relative',
//         display: 'flex',
//         flexDirection: isH ? 'row' : 'column',
//         justifyContent: isH ? mainAxisAlignment : crossAxisAlignment,
//         alignItems: isH ? crossAxisAlignment : mainAxisAlignment,
//         flexWrap: 'nowrap',
//         gap: direction === DirectionType.Horizontal ? `${horizontalGap}px` : `${verticalGap}px`,
//         padding: padding.join('px ') + 'px'
//       };

//       // 水平布局换行
//       if (flowWarp) {
//         layoutStyle.flexWrap = 'wrap';
//         layoutStyle.alignContent = 'start';
//       }

//       // 父级：固定尺寸 / 适应内容
//       switch (frame.limitSize.width.resize) {
//         case ResizeType.Fixed:
//           layoutStyle.width = `${width}px`;
//           break;
//         case ResizeType.Adapt:
//           layoutStyle.width = 'auto';
//       }

//       switch (frame.limitSize.height.resize) {
//         case ResizeType.Fixed:
//           layoutStyle.height = `${height}px`;
//           break;
//         case ResizeType.Adapt:
//           layoutStyle.height = 'auto';
//       }

//       // 最大最小限制
//       // if (isNumber(frame.limitSize.width.max)) {
//       //   layoutStyle.maxWidth = `${frame.limitSize.width.max}px`;
//       // }
//       // if (isNumber(frame.limitSize.width.min)) {
//       //   layoutStyle.minWidth = `${frame.limitSize.width.min}px`;
//       // }
//       // if (isNumber(frame.limitSize.height.max)) {
//       //   layoutStyle.maxHeight = `${frame.limitSize.height.max}px`;
//       // }
//       // if (isNumber(frame.limitSize.height.min)) {
//       //   layoutStyle.minHeight = `${frame.limitSize.height.min}px`;
//       // }
//     }
//     return { ...style, ...layoutStyle };
//   };
