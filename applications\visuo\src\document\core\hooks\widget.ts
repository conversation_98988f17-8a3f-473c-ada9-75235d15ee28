import type { AxisField, Paragraph, WidgetBlock } from '../models';

/**
 * 组件公共方法
 * <AUTHOR>
 */
export const useWidget = () => {
  /**
   * 获取组件数据
   */
  const getWidgetData = (fields: AxisField[], data: any, template?: string) => {
    if (!template) {
      return '';
    }
    const match = spliceBraces(template);

    let result = template;

    // 遍历匹配到的字段名，替换模板中的占位符
    match.forEach((chart: string) => {
      const str = analysisChart(chart, fields, data);
      // str === 0 || str === false 处理变量值为0和false的情况
      result = result.replace('{' + chart + '}', str || str === 0 || str === false ? str : '');
    });

    return result;
  };

  /**
   * 解析单个{}中数据
   * @param chart 字段名或表达式
   * @param fields 字段数组
   * @param data 数据对象
   */
  const analysisChart = (chart: string, fields?: Array<AxisField>, data?: any) => {
    if (chart.indexOf('|') === -1) {
      // 没有样式分隔符，直接解析字段
      if (fields) {
        return analysisField(chart, fields, data);
      }
    } else {
      // 有样式分隔符，处理样式
      const index = chart.indexOf('|');
      const chartArr = [chart.slice(0, index), chart.slice(index + 1, chart.length)];
      const style = chartArr[0]; // 简化处理，直接使用样式字符串
      const _match = spliceBraces(chartArr[1]);

      if (!_match.length) {
        return '<span style="' + style + '">' + chartArr[1] + '</span>';
      } else {
        let content = chartArr[1];
        _match.forEach((m: string) => {
          const str = analysisChart(m, fields, data);
          content = content.replace('{' + m + '}', str || str === 0 || str === false ? str : '');
        });
        return '<span style="' + style + '">' + content + '</span>';
      }
    }
  };

  /**
   * 解析字段返回数据值
   * @param chart 字段名或索引
   * @param fields 字段数组
   * @param data 数据对象
   */
  const analysisField = (chart: string, fields: Array<AxisField>, data: any) => {
    // 检查是否为数字索引
    const filedIndex = Number.parseInt(chart, 10);
    if (!isNaN(filedIndex) && fields[filedIndex]) {
      // 使用索引访问字段
      const fieldName = fields[filedIndex].fieldName as string;
      return data[fieldName];
    } else {
      // 使用字段名或别名查找
      const field = fields.find((f) => f.fieldName === chart || f.fieldAlias === chart);
      if (field) {
        const dataKey = field.fieldName || field.fieldAlias || chart;
        return data[dataKey];
      } else {
        // 直接使用字段名从data中获取
        return data[chart];
      }
    }
  };

  /**
   * 获取组件样式
   * @param widget 组件
   * @returns 组件样式
   */
  const getWidgetStyle = (widget: WidgetBlock) => {
    const style = widget?.options?.style;

    return style;
  };

  /**
   * 获取组件配置
   * @param widget 组件
   * @returns 组件配置
   */
  const getWidgetOptions = (widget: WidgetBlock) => {
    const option = widget?.options;
    return option;
  };

  /**
   * 匹配括号内容
   * @param template 模板字符串
   * @returns 匹配到的括号内容数组
   */
  const spliceBraces = (template: string): Array<string> => {
    const matches: Array<string> = [];
    let currentContent = '';
    let inBrace = false;

    for (let i = 0; i < template.length; i++) {
      const char = template[i];

      if (char === '{' && !inBrace) {
        // 开始新的括号内容
        inBrace = true;
        currentContent = '';
      } else if (char === '}' && inBrace) {
        // 找到完整的括号对
        matches.push(currentContent);
        inBrace = false;
        currentContent = '';
      } else if (inBrace) {
        // 在括号内，收集内容
        currentContent += char;
      }
    }

    return matches;
  };

  return {
    getWidgetData,
    getWidgetStyle,
    getWidgetOptions
  };
};
