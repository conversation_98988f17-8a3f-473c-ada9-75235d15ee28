import { Graph, type MenuPosition } from '@vis/document-core';
import { defineComponent, computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import type { PropType } from 'vue';
import { DesignAction, DesignActionGroup } from '@vis/page-main/modules/design/models';

/**
 * 右键菜单组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-store-context-menu',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    position: {
      type: Object as PropType<MenuPosition>,
      required: true
    },
    menu: {
      type: Array as PropType<(DesignAction | DesignActionGroup | any)[]>,
      required: true
    },
    node: {
      type: Object as PropType<Graph>,
      default: null
    }
  },
  emits: ['action-click', 'close'],
  setup(props, { emit }) {
    const menuRef = ref<HTMLElement>();

    const visible = ref<boolean>(false);
    const position = ref<MenuPosition>({ x: 100, y: 100 });
    const menus = ref([]);
    const show = () => {
      visible.value = true;
    }
    const hide = () => {
      visible.value = false;
    }

    // #region 滚动条高度和最大高度计算
    const SAFE_MARGIN = 10; // 安全边距
    const MAX_HEIGHT = 700; // 最大高度
    const scrollMaxHeight = ref<number>(MAX_HEIGHT);

    /**
     * 计算滚动区域高度
     * @returns number
     * */ 
    const scrollHeight = computed(() => {
      // padding: 8/8; item: content 24 margin 4/4; separator: content 1 margin 4/4
      // 计算定高：8 + 28 * (item.length - 1 - separator.length) + 5 * (separator.length - 1) + 4 + 8
      const initialHeight = 8 + 8 + 4;
      console.log(props.menu, 'Array.isArray(cur)');
      const traverse = (menus: (DesignAction | DesignActionGroup | any)[]) => {
        return menus.reduce((pre, cur, index, arr) => {
          return pre + (Array.isArray(cur) ? traverse(cur) + 5 : 28);
        }, 0);
      }
      return traverse(props.menu) + initialHeight;
      // return props.menu.reduce((pre, cur) => {
      //   return pre + (cur.name === 'separator' ? 5 : 28);
      // }, initialHeight);
    });
    // #endregion

    // #region 计算菜单位置
    const adjustedPosition = ref<MenuPosition>({ x: 0, y: 0 });
    const isPositionAdjusted = ref(false);

    const menuStyle = computed(() => {
      const position = isPositionAdjusted.value ? adjustedPosition.value : props.position;
      return {
        position: 'fixed' as const,
        left: `${position.x}px`,
        top: `${position.y}px`,
        zIndex: 6000
      };
    });

    // 动态最大高度计算
    const updateScrollMaxHeight = () => {
      const innerSpace = window.innerHeight - SAFE_MARGIN * 2;
      scrollMaxHeight.value = Math.min(innerSpace, MAX_HEIGHT);
    };

    const adjustMenuPosition = () => {
      if (!props.visible || !menuRef.value) return;
      // DOM加载完调整位置
      nextTick(() => {
        const menu = menuRef.value;

        // 获取菜单容器和视口的尺寸
        const rect = menu!.getBoundingClientRect();
        const menuWidth = rect.width;
        const menuHeight = rect.height;
        const vw = window.innerWidth;
        const vh = window.innerHeight;

        // 使用当前的位置作为基准
        let { x, y } = isPositionAdjusted.value ? adjustedPosition.value : props.position;

        // 水平位置计算
        const rightSpace = vw - x - SAFE_MARGIN; // 鼠标到右侧距离
        if (rightSpace < menuWidth) {
          x = vw - menuWidth - SAFE_MARGIN;
        }

        // 垂直位置计算 - 高度动态变化
        const bottomSpace = vh - y - SAFE_MARGIN; // 鼠标到底部距离
        if (bottomSpace < menuHeight) {
          y = vh - menuHeight - SAFE_MARGIN;
        }

        // 确保不超出左边界
        if (x < SAFE_MARGIN) {
          x = SAFE_MARGIN;
        }

        // 确保不超出上边界
        if (y < SAFE_MARGIN) {
          y = SAFE_MARGIN;
        }

        // 更新响应式位置数据
        adjustedPosition.value = { x, y };
        isPositionAdjusted.value = true;
      });
    };
    // #endregion

    // #region 菜单事件处理
    const onActionClick = (action: DesignAction) => {
      if (action.disable) return;
      emit('action-click', action, props.node);
      emit('close');
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (!props.visible) return;

      const target = event.target as HTMLElement;
      if (menuRef.value && !menuRef.value.contains(target)) {
        emit('close');
      }
    };
    // #endregion

    // 监听 visible 和 position 属性变化，支持连续右键触发
    watch(
      (): [Boolean, MenuPosition] => [props.visible, props.position],
      ([visible, position], [o_visible, o_position]) => {
        // 只有在菜单可见或位置确实变化时重新调整
        if (visible || (o_position && (position.x !== o_position.x || position.y !== o_position.y))) {
          nextTick(() => {
            updateScrollMaxHeight();
            adjustMenuPosition();
          });
        }
        isPositionAdjusted.value = false;
      },
      { deep: true }
    );

    onMounted(() => {
      document.addEventListener('click', handleClickOutside);
    });

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
    });

    return {
      scrollMaxHeight,
      scrollHeight,
      menuRef,
      menuStyle,

      onActionClick
    };
  }
});
