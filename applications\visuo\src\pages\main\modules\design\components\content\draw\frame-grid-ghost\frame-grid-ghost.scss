.#{$vis-prefix}-frame-grid-ghost {
  .left {
    @apply absolute w-50px;
    .hand {
      @apply h-full;
    }
  }
  .top {
    @apply absolute h-50px;
    .hand {
      @apply w-full h-full;
    }
  }

  .hand {
    .item {
      @apply flex justify-center items-center;

      .tip {
        @apply invisible;
        .text {
          @apply text-white text-10px bg-#4af p-1 rounded;
        }
        .q-field {
          @apply w-28px;
          &__control-container,
          &__control,
          &__native {
            @apply h-20px min-h-20px leading-20px;
          }
          &__native {
            @apply text-10px text-center;
          }
        }
      }
      &:hover {
        .tip {
          @apply visible;
        }
      }
    }
  }
}
