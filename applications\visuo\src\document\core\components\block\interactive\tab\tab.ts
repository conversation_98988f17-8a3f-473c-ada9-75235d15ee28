import { computed, defineComponent, ref, type CSSProperties, type PropType, onMounted, watch, nextTick } from 'vue';
import {
  AxisField,
  Block,
  DirectionType,
  Effects,
  FillPaints,
  Font,
  IconPosition,
  ImageType,
  Stroke,
  Tab,
  TabOption,
  TabResizeType,
  Text,
  TextAlign,
  VerticalAlign
} from '../../../../models';
import { useBase, useFill, useUiStyle } from '../../../../hooks';
import { AttachmentService } from '@hetu/platform-shared';
import type { Records } from '@hetu/util';

/**
 * <AUTHOR>
 * 选项卡组件
 */
export default defineComponent({
  name: 'vis-tab',
  props: {
    widget: {
      type: Object as PropType<Tab>,
      required: true
    },
    block: {
      type: Object as PropType<Block>,
      required: true
    }
  },
  setup(props) {
    const { getStrokeStyle, getEffectStyle, getTextStyle, getTextEffectStyle, getTextOverflow } = useUiStyle();
    const { getFillPaintsStyle } = useFill();
    const { getWidgetFieldData, loadStaticData } = useBase();

    const data = ref<TabOption[]>([]);

    const tabBlock = computed(() => props.block);
    const tabOption = computed(() => props.widget.options);
    const marqueeRefs = ref<HTMLElement[]>([]);

    const tabContentRef = ref<HTMLElement[]>([]);

    const changLineRefs = ref<HTMLElement[]>([]);

    const tabRef = ref<HTMLElement[]>([]);

    const hoverIndex = ref(-1);

    //#region 选中
    const getIndex = () => {
      if (typeof tabOption.value.defaultIndex === 'number') return [tabOption.value.defaultIndex - 1];
      if (tabOption.value.multiple) {
        // 多选
        return tabOption.value.defaultIndex
          .split(',')
          .map(Number)
          .filter((num) => !isNaN(num))
          .map((num) => num - 1);
      } else {
        // 单选
        const defaultArr = tabOption.value.defaultIndex.split(',');
        if (defaultArr.length) {
          // 取第一个
          return isNaN(Number(defaultArr[0])) ? [] : [Number(defaultArr[0]) - 1];
        } else {
          // 没有项返回空数组
          return [];
        }
      }
    };
    /**
     * 选中项
     */
    const selectedIndexs = ref(getIndex());

    watch(
      () => tabOption.value.defaultIndex,
      () => {
        selectedIndexs.value = getIndex();
      }
    );

    watch(
      () => tabOption.value.multiple,
      (val) => {
        if (!val) {
          selectedIndexs.value = getIndex();
        }
      }
    );

    const handleActive = (idx: number) => {
      if (tabOption.value.multiple) {
        // 多选
        if (selectedIndexs.value.includes(idx)) {
          const index = selectedIndexs.value.findIndex((item) => item === idx);
          index != -1 && selectedIndexs.value.splice(index, 1);
        } else {
          selectedIndexs.value.push(idx);
        }
      } else {
        // 单选
        if (selectedIndexs.value.includes(idx)) return;
        selectedIndexs.value = [idx];
      }
    };

    //#endregion 选中

    /**
     * 跑马灯滚动速度值
     */
    const SPEED = 30;

    /**
     * 存储每个tab的是否溢出
     */
    const textOverflowFlags = ref<boolean[]>(Array((props.widget as any)?.staticData?.length || 0).fill(false));

    const layout = computed(() => tabOption.value.layout);

    const tabStyle = computed(() => {
      let grid;
      if (layout.value.direction === DirectionType.Grid) {
        grid = {
          display: 'grid',
          gridTemplateColumns: `repeat(${layout.value.column}, ${
            tabOption.value.resizeX === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.width + 'px'
          })`,
          gridTemplateRows: `repeat(${layout.value.row},${
            tabOption.value.resizeY === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.height + 'px'
          })`,
          gridColumnGap: layout.value.gutter[1] + 'px',
          columnGap: layout.value.gutter[1] + 'px',
          gridRowGap: layout.value.gutter[0] + 'px',
          rowGap: layout.value.gutter[0] + 'px'
        };
      } else if (layout.value.direction === DirectionType.Horizontal) {
        if (layout.value.flowWrap && tabOption.value.resizeX === TabResizeType.Fixed) {
          //  行列间距都生效
          grid = {
            display: 'grid',
            gridTemplateColumns: `repeat(auto-fill, ${tabOption.value.width}px)`,
            gridColumnGap: layout.value.gutter[1] + 'px',
            columnGap: layout.value.gutter[1] + 'px',
            gridRowGap: layout.value.gutter[0] + 'px',
            rowGap: layout.value.gutter[0] + 'px',
            gridAutoRows: tabOption.value.resizeY === TabResizeType.Adapt ? 'auto' : tabOption.value.height + 'px'
          };
        } else {
          // 只有列间距
          grid = {
            display: 'grid',
            gridAutoFlow: 'column',
            gridTemplateRows: `repeat(1,${
              tabOption.value.resizeY === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.height + 'px'
            })`,
            gridColumnGap: layout.value.gutter[1] + 'px',
            columnGap: layout.value.gutter[1] + 'px',
            gridAutoColumns: tabOption.value.resizeX === TabResizeType.Adapt ? 'auto' : tabOption.value.width + 'px'
          };
        }
      } else if (layout.value.direction === DirectionType.Vertical) {
        // 说明垂直排布
        grid = {
          display: 'grid',
          gridTemplateColumns: `repeat(1, ${
            tabOption.value.resizeX === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.width + 'px'
          })`,
          gridRowGap: layout.value.gutter[0] + 'px',
          rowGap: layout.value.gutter[0] + 'px',
          gridAutoRows: tabOption.value.resizeY === TabResizeType.Adapt ? 'auto' : tabOption.value.height + 'px'
        };
      }
      return {
        height:
          tabOption.value.resizeX === TabResizeType.Fixed &&
          layout.value.scrollbar &&
          tabOption.value.resizeY === TabResizeType.Adapt
            ? props.block.height + 'px'
            : '100%',
        ...grid
      };
    });

    const tabItemStyle = (index: number) => {
      const style = tabOption.value.style;
      const width = tabOption.value.resizeX === TabResizeType.Adapt ? '100%' : tabOption.value.width + 'px';
      const height = tabOption.value.resizeY === TabResizeType.Adapt ? '100%' : tabOption.value.height + 'px';
      let fill, stroke, effects, isRow;
      if (hoverIndex.value === index && style.hover && style.hover.visible) {
        // 悬浮样式处理
        // 说明有样式
        fill = getFillPaintsStyle([style.hover.background]);
        stroke = getStrokeStyle(style.hover.border);
        effects = getEffectStyle(style.hover.shadow);
        isRow =
          style.hover.icon.position === IconPosition.Left || style.hover.icon.position === IconPosition.Right
            ? true
            : false;
      } else if (selectedIndexs.value.includes(index) && style.active && style.active.visible) {
        // 选中样式处理
        fill = getFillPaintsStyle([style.active.background]);
        stroke = getStrokeStyle(style.active.border);
        effects = getEffectStyle(style.active.shadow);

        isRow =
          style.active.icon.position === IconPosition.Left || style.active.icon.position === IconPosition.Right
            ? true
            : false;
      } else {
        // 普通样式处理
        fill = getFillPaintsStyle(style.background ? [style.background as FillPaints] : undefined);
        stroke = getStrokeStyle(style.border);
        effects = getEffectStyle(style.shadow);

        isRow = tabOption.value.icon
          ? tabOption.value.icon.position === IconPosition.Left || tabOption.value.icon.position === IconPosition.Right
            ? true
            : false
          : true;
      }

      // 删除未设置样式
      for (const key in fill) {
        if (fill[key] === 'initial') {
          delete fill[key];
        }
      }

      for (const key in stroke) {
        if (stroke[key] === 'initial') {
          delete stroke[key];
        }
      }

      for (const key in effects) {
        if (effects[key] === 'initial') {
          delete effects[key];
        }
      }

      // 文本溢出处理
      return {
        ...fill,
        ...stroke,
        ...effects,
        width,
        height,
        borderRadius: style.radius.map((r) => r + 'px').join(' '),
        display: 'flex',
        flexDirection: isRow ? 'row' : 'column',
        // 水平对齐
        [style.textDirection === DirectionType.Horizontal ? 'justifyContent' : 'alignItems']:
          style.font.alignHorizontal === TextAlign.Center
            ? 'center'
            : style.font.alignHorizontal === TextAlign.Left
            ? 'flex-start'
            : 'flex-end',
        // 垂直对齐
        [style.textDirection === DirectionType.Horizontal ? 'alignItems' : 'justifyContent']:
          style.font.alignVertical === VerticalAlign.Center
            ? 'center'
            : style.font.alignVertical === VerticalAlign.Top
            ? 'flex-start'
            : 'flex-end'
      } as CSSProperties;
    };

    const tabContentStyle = (index: number) => {
      const style = tabOption.value.style;

      let textStyle, textShadow, isFullHeight, isFullWidth;
      const allHeight = tabOption.value.icon
        ? tabOption.value.icon.height +
          (tabOption.value.icon.position === IconPosition.Top || tabOption.value.icon.position === IconPosition.Bottom
            ? tabOption.value.icon.gutter
            : 0)
        : 0;
      const allWidth = tabOption.value.icon
        ? tabOption.value.icon.width +
          (tabOption.value.icon.position === IconPosition.Left || tabOption.value.icon.position === IconPosition.Right
            ? tabOption.value.icon.gutter
            : 0)
        : 0;
      if (hoverIndex.value === index && style.hover && style.hover.visible) {
        textStyle = getTextStyle(style.hover.font as unknown as Text, style.textDirection === DirectionType.Vertical);
        textShadow = getTextEffectStyle(style.hover.font.textEffects, 'hover');
        isFullHeight =
          style.hover.icon.position === IconPosition.Left || style.hover.icon.position === IconPosition.Right
            ? true
            : false;
        isFullWidth =
          style.hover.icon.position === IconPosition.Top || style.hover.icon.position === IconPosition.Bottom
            ? true
            : false;
      } else if (selectedIndexs.value.includes(index) && style.active && style.active.visible) {
        textStyle = getTextStyle(style.active.font as unknown as Text, style.textDirection === DirectionType.Vertical);
        textShadow = getTextEffectStyle(style.active.font.textEffects, 'active');
        isFullHeight =
          style.active.icon.position === IconPosition.Left || style.active.icon.position === IconPosition.Right
            ? true
            : false;
        isFullWidth =
          style.active.icon.position === IconPosition.Top || style.active.icon.position === IconPosition.Bottom
            ? true
            : false;
      } else {
        textStyle = getTextStyle(style.font as unknown as Text, style.textDirection === DirectionType.Vertical);
        textShadow = getTextEffectStyle(style.font.textEffects);
        isFullHeight = tabOption.value.icon
          ? tabOption.value.icon.position === IconPosition.Left || tabOption.value.icon.position === IconPosition.Right
            ? true
            : false
          : true;

        isFullWidth = tabOption.value.icon
          ? tabOption.value.icon.position === IconPosition.Top || tabOption.value.icon.position === IconPosition.Bottom
            ? true
            : false
          : true;
      }

      for (const key in textStyle) {
        if (textStyle[key] === 'initial') {
          delete textStyle[key];
        }
      }

      for (const key in textShadow) {
        if (textShadow[key] === 'initial') {
          delete textShadow[key];
        }
      }

      return {
        ...overflowStyle.value,
        ...textStyle,
        ...textShadow,
        justifyContent:
          style.overflow === 3 && textOverflowFlags.value[index] ? 'flex-start' : textStyle!.justifyContent,
        writingMode: style.textDirection === DirectionType.Horizontal ? 'horizontal-tb' : 'vertical-lr',
        textOrientation: 'upright',

        flex: textOverflowFlags.value[index] ? '1' : dealOverflow(index),
        height: isFullHeight
          ? '100%'
          : textOverflowFlags.value[index]
          ? `${
              tabOption.value.icon
                ? tabContentRef.value &&
                  tabContentRef.value[index] &&
                  allHeight < tabContentRef.value[index].clientHeight
                  ? tabContentRef.value[index].clientHeight - allHeight
                  : 0
                : tabRef.value[index] && tabRef.value[index].clientHeight
            }px`
          : 'auto',
        width: isFullWidth
          ? '100%'
          : textOverflowFlags.value[index]
          ? `${
              tabOption.value.icon
                ? tabContentRef.value && tabContentRef.value[index] && allWidth < tabContentRef.value[index].clientWidth
                  ? tabContentRef.value[index].clientWidth - allWidth
                  : 0
                : tabRef.value[index] && tabRef.value[index].clientWidth
            }px`
          : 'auto'
      } as CSSProperties;
    };

    const dealOverflow = (index: number) => {
      if (tabOption.value.style.overflow === 1) {
        if (tabOption.value.style.textDirection === DirectionType.Horizontal) {
          return marqueeRefs.value[index] && marqueeRefs.value[index].scrollWidth > marqueeRefs.value[index].clientWidth
            ? '1'
            : '0 1 auto';
        } else {
          return marqueeRefs.value[index] &&
            marqueeRefs.value[index].scrollHeight > marqueeRefs.value[index].clientHeight
            ? '1'
            : '0 1 auto';
        }
      } else if (tabOption.value.style.overflow === 2) {
        // 换行
        if (tabOption.value.style.textDirection === DirectionType.Horizontal) {
          // 文本水平 检测高度对比
          if (marqueeRefs.value[index] && changLineRefs.value[index]) {
            return marqueeRefs.value[index].clientHeight > changLineRefs.value[index].clientHeight ? '1' : '0 1 auto';
          } else {
            return '0 1 auto';
          }
        } else {
          // 文本垂直 检测宽度对比
          if (marqueeRefs.value[index] && changLineRefs.value[index]) {
            return marqueeRefs.value[index].clientWidth > changLineRefs.value[index].clientWidth ? '1' : '0 1 auto';
          } else {
            return '0 1 auto';
          }
        }
      }
    };

    const overflowStyle = computed(() => {
      const style = tabOption.value.style;
      const overflow = getTextOverflow(style.overflow);
      return overflow as CSSProperties;
    });

    // 检测文本是否溢出
    const checkTextOverflow = () => {
      nextTick(() => {
        if (!marqueeRefs.value || marqueeRefs.value.length === 0) return;

        // 确保textOverflowFlags数组长度与data相同
        if (textOverflowFlags.value.length !== data.value.length) {
          textOverflowFlags.value = Array(data.value.length).fill(false);
        }

        // 遍历所有元素检查溢出
        marqueeRefs.value.forEach((el, index) => {
          if (!el) return;
          const parentEl = el.parentElement;
          if (!parentEl) return;
          // 检查元素是否溢出
          const isOverflowing =
            tabOption.value.style.textDirection === DirectionType.Horizontal
              ? el.clientWidth > parentEl.clientWidth + 2
              : el.clientHeight > parentEl.clientHeight + 2;
          textOverflowFlags.value[index] = isOverflowing;
        });

        // 强制更新
        textOverflowFlags.value = [...textOverflowFlags.value];
      });
    };

    // 获取每个项目的溢出样式
    const getItemOverflowStyle = (index: number) => {
      const style = tabOption.value.style;

      const textStyle = getTextStyle(style.font as unknown as Text, style.textDirection === DirectionType.Vertical);
      // 确保索引有效
      if (index < 0 || index >= (textOverflowFlags.value.length || 0)) {
        return getTextOverflow(style.overflow) as CSSProperties;
      }
      // 只有在overflow为3（跑马灯）且文本确实溢出时才应用动画
      if (style.overflow === 3 && textOverflowFlags.value[index]) {
        const speed = tabOption.value.style.scrollSpeed > 0 ? SPEED * tabOption.value.style.scrollSpeed : SPEED;
        let duration = SPEED;
        if (marqueeRefs.value[index]) {
          const length =
            style.textDirection === DirectionType.Horizontal
              ? marqueeRefs.value[index].clientWidth
              : marqueeRefs.value[index].clientHeight;
          duration = length / speed;
        }
        return {
          display: 'inline-block',
          animation: `${
            style.textDirection === DirectionType.Horizontal ? DirectionType.Horizontal : DirectionType.Vertical
          }-scroll ${duration}s linear infinite`,
          paddingRight: (style.textDirection === DirectionType.Horizontal ? 5 : 0) + 'px',
          paddingBottom: (style.textDirection === DirectionType.Horizontal ? 0 : 5) + 'px',
          color: textStyle.color,
          backgroundImage: textStyle.backgroundImage,
          backgroundPosition: textStyle.backgroundPosition,
          backgroundRepeat: textStyle.backgroundRepeat,
          backgroundClip: textStyle.backgroundClip,
          backgroundSize: textStyle.backgroundSize,
          webkitBackgroundClip: textStyle.backgroundClip
        };
      }
      return getTextOverflow(style.overflow) as CSSProperties;
    };

    // 监听数据变化和样式变化，重新检测溢出
    watch(
      [() => data.value, () => props.block.width, () => props.block.height, () => tabOption.value],
      () => {
        checkTextOverflow();
      },
      { deep: true }
    );

    // 监听布局
    watch([() => tabOption.value.layout.direction, () => data.value.length], (val) => {
      const blockWidth = props.block.width;
      const blockHeight = props.block.height;
      if (val[0] === DirectionType.Horizontal) {
        // 切换为水平 区分情况
        if (tabOption.value.resizeX === TabResizeType.Adapt) {
          // 宽度为自适应 比较块的宽高，值大的为宽度，值小的为高度
          if (tabOption.value.resizeY === TabResizeType.Adapt) {
            tabBlock.value.height = blockWidth > blockHeight ? blockHeight : blockWidth;
          } else {
            tabBlock.value.height = tabOption.value.height;
          }
          tabBlock.value.width = blockWidth > blockHeight ? blockWidth : blockHeight;
        } else {
          // 宽度为固定值的情况下，块的宽度应是 选项卡的数量*宽度+ 横向间距*(选项卡数量-1)
          tabBlock.value.width =
            data.value.length * tabOption.value.width + (data.value.length - 1) * tabOption.value.layout.gutter[1];

          // 高度自适应
          tabBlock.value.height =
            tabOption.value.resizeY === TabResizeType.Adapt ? tabOption.value.width : tabOption.value.height;
        }
      } else if (val[0] === DirectionType.Vertical) {
        // 垂直显示
        if (tabOption.value.resizeY === TabResizeType.Adapt) {
          if (tabOption.value.resizeX === TabResizeType.Adapt) {
            // 行列都自适应的情况下块的宽高切换
            tabBlock.value.width = blockWidth > blockHeight ? blockHeight : blockWidth;
          } else {
            // 行高自适应  列宽有固定值
            tabBlock.value.width = tabOption.value.width;
          }
          tabBlock.value.height = blockWidth > blockHeight ? blockWidth : blockHeight;
        } else {
          tabBlock.value.width =
            tabOption.value.resizeX === TabResizeType.Adapt ? tabOption.value.height : tabOption.value.width;

          // 高度固定高度的情况下
          tabBlock.value.height =
            data.value.length * tabOption.value.height + (data.value.length - 1) * tabOption.value.layout.gutter[0];
        }
      } else {
        // 网格处理
        // 获取最优行列数
        const { cols, rows } = calculateOptimalGrid(data.value.length);
        tabOption.value.layout.column = cols;
        tabOption.value.layout.row = rows;
        // 行列都自适应 都按照每个tab 宽度为100px 高度为50px 进行计算 有固定值则按照固定值计算
        const width = tabOption.value.resizeX === TabResizeType.Adapt ? 100 : tabOption.value.width;
        const height = tabOption.value.resizeY === TabResizeType.Adapt ? 50 : tabOption.value.height;
        tabBlock.value.width = cols * width + (cols - 1) * tabOption.value.layout.gutter[1];
        tabBlock.value.height = rows * height + (rows - 1) * tabOption.value.layout.gutter[0];
      }
    });

    const calculateOptimalGrid = (length: number) => {
      if (length <= 0) return { rows: 0, cols: 0 };

      let bestRows = 1;
      let bestCols = length;
      let minDiff = Math.abs(bestRows - bestCols);

      // 从1到平方根遍历可能的行数
      for (let rows = 2; rows <= Math.sqrt(length); rows++) {
        const cols = Math.ceil(length / rows);
        const diff = Math.abs(rows - cols);

        // 如果找到更小的差值，更新最佳行列数
        if (diff < minDiff) {
          minDiff = diff;
          bestRows = rows;
          bestCols = cols;
        }
      }

      // 检查是否交换行列能得到更小的差值
      if (Math.abs(bestCols - bestRows) > Math.abs(bestRows - bestCols)) {
        [bestRows, bestCols] = [bestCols, bestRows];
      }

      return {
        rows: bestRows,
        cols: bestCols
      };
    };

    //#region 图标
    const getIconStyle = computed(() => {
      let position = 'Right';
      if (tabOption.value.icon?.position === IconPosition.Right) {
        position = 'Left';
      } else if (tabOption.value.icon?.position === IconPosition.Top) {
        position = 'Bottom';
      } else if (tabOption.value.icon?.position === IconPosition.Bottom) {
        position = 'Top';
      }
      return {
        [`margin${position}`]: tabOption.value.icon?.gutter + 'px',
        width: tabOption.value.icon?.width + 'px',
        height: tabOption.value.icon?.height + 'px',
        borderRadius: '0px'
      };
    });

    const getHoverIconStyle = computed(() => {
      let position = 'Right';
      if (tabOption.value.style.hover?.icon?.position === IconPosition.Right) {
        position = 'Left';
      } else if (tabOption.value.style.hover?.icon?.position === IconPosition.Top) {
        position = 'Bottom';
      } else if (tabOption.value.style.hover?.icon?.position === IconPosition.Bottom) {
        position = 'Top';
      }
      return {
        [`margin${position}`]: tabOption.value.style.hover?.icon?.gutter + 'px',
        width: tabOption.value.style.hover?.icon?.width + 'px',
        height: tabOption.value.style.hover?.icon?.height + 'px',
        borderRadius: '0px'
      };
    });

    const getActiveIconStyle = computed(() => {
      let position = 'Right';
      if (tabOption.value.style.active?.icon?.position === IconPosition.Right) {
        position = 'Left';
      } else if (tabOption.value.style.active?.icon?.position === IconPosition.Top) {
        position = 'Bottom';
      } else if (tabOption.value.style.active?.icon?.position === IconPosition.Bottom) {
        position = 'Top';
      }

      return {
        [`margin${position}`]: tabOption.value.style.active?.icon?.gutter + 'px',
        width: tabOption.value.style.active?.icon?.width + 'px',
        height: tabOption.value.style.active?.icon?.height + 'px',
        borderRadius: '0px'
      };
    });

    /**
     * 默认状态下的图标url
     */
    const iconUrl = computed(() => {
      if (!tabOption.value.icon || !tabOption.value.icon.image) return '';
      const { type, url } = tabOption.value.icon.image;
      return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
    });

    const shouldShowIcon = (idx: number, position: string) => {
      if (hoverIndex.value === idx) {
        // 说明鼠标有悬浮
        if (tabOption.value.style.hover?.visible) {
          //开了眼睛
          return tabOption.value.style.hover.icon.image?.url && showPositionIcon(position, 'hover') ? true : false;
        } else {
          // 没开眼睛
          return selectedIndexs.value.includes(idx) && tabOption.value.style.active?.visible
            ? tabOption.value.style.active.icon.image?.url && showPositionIcon(position, 'active')
              ? true
              : false
            : !!tabOption.value.icon;
        }
      } else {
        return selectedIndexs.value.includes(idx) && tabOption.value.style.active?.visible
          ? tabOption.value.style.active.icon.image?.url && showPositionIcon(position, 'active')
            ? true
            : false
          : !!tabOption.value.icon && showPositionIcon(position);
      }
    };

    const showPositionIcon = (position: string, status?: 'hover' | 'active') => {
      if (position === 'before') {
        if (status) {
          return tabOption.value.style[status]?.icon.position === IconPosition.Left ||
            tabOption.value.style[status]?.icon.position === IconPosition.Top
            ? true
            : false;
        } else {
          return tabOption.value.icon?.position === IconPosition.Left ||
            tabOption.value.icon?.position === IconPosition.Top
            ? true
            : false;
        }
      } else {
        if (status) {
          return tabOption.value.style[status]?.icon.position === IconPosition.Right ||
            tabOption.value.style[status]?.icon.position === IconPosition.Bottom
            ? true
            : false;
        } else {
          return tabOption.value.icon?.position === IconPosition.Right ||
            tabOption.value.icon?.position === IconPosition.Bottom
            ? true
            : false;
        }
      }
    };

    /**
     * 悬浮状态下的iconUrl
     */
    const hoverIconUrl = (idx: number) => {
      if (
        hoverIndex.value === idx &&
        tabOption.value.style.hover?.visible &&
        tabOption.value.style.hover?.icon.image?.url
      ) {
        const { type, url } = tabOption.value.style.hover.icon.image;
        return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
      }
      return;
    };

    /**
     * 选中状态下的iconUrl
     * @param idx
     * @returns
     */
    const activeIconUrl = (idx: number) => {
      if (
        selectedIndexs.value.includes(idx) &&
        tabOption.value.style.active?.visible &&
        tabOption.value.style.active?.icon.image?.url
      ) {
        const { type, url } = tabOption.value.style.active.icon.image;
        return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
      }
      return;
    };

    //#endregion 图标

    //#region 数据
    const transformData = (originalData: any, dataMapping: Records<AxisField[]>) => {
      return originalData.map((item: any) => {
        const newItem: { [key: string]: any } = {};
        // 遍历 dataMapping 的所有 key（如 text、value、disabled 或其他）
        Object.keys(dataMapping).forEach((newKey) => {
          // 获取该 key 对应的映射配置（如 [{ fieldName: "A", ... }]）
          const mappingList = dataMapping[newKey];

          // 遍历映射配置（支持一个 key 对应多个字段，但通常只有一个）
          mappingList.forEach((mapping) => {
            const originalField = mapping.fieldName;

            if (originalField && Object.prototype.hasOwnProperty.call(item, originalField)) {
              newItem[newKey] = item[originalField];
            }
          });
        });

        return newItem;
      });
    };
    const loadWidgetData = async () => {
      return new Promise((resolve) => {
        if (props.widget.datasetType === 'static') {
          loadStaticData().then((res: any) => {
            if (res) {
              data.value = transformData(res, props.widget.dataMapping);
            }
            resolve(res);
          });
          return;
        }
        getWidgetFieldData(
          new Promise((resolve) => {
            setTimeout(() => {
              console.log('ParagraphComponent: 重写加载选项卡数据', props.widget.id);
              resolve(true);
            }, 5000);
          })
        ).then((res) => {
          resolve(res);
        });
      });
    };
    //#endregion 数据

    onMounted(() => {
      // 确保DOM渲染完成后再检测
      checkTextOverflow();
    });

    return {
      tabOption,
      data,
      tabStyle,
      layout,
      TabResizeType,
      overflowStyle,
      marqueeRefs,
      tabContentRef,
      tabRef,
      changLineRefs,
      textOverflowFlags,
      getIconStyle,
      getHoverIconStyle,
      getActiveIconStyle,
      iconUrl,
      hoverIndex,
      selectedIndexs,
      tabItemStyle,
      tabContentStyle,
      getItemOverflowStyle,
      shouldShowIcon,
      hoverIconUrl,
      handleActive,
      activeIconUrl,
      loadWidgetData
    };
  }
});
