import { defineComponent, ref, computed } from 'vue';
import type { MenuPosition, ContextMenuType, ContextMenuOptions } from '@vis/document-core';
import ContextMenuItem from './context-menu-item/index.vue';

export default defineComponent({
  name: 'vis-context-menu',
  components: { ContextMenuItem },
  props: {},
  emits: ['show', 'hide'],
  setup(props, { emit, expose }) {
    const visible = ref<boolean>(false);
    const position = ref<MenuPosition>({ x: 0, y: 0 });
    const contextMenu = ref<ContextMenuType>([]);

    const menuStyle = computed(() => {
      // const position = isPositionAdjusted.value ? adjustedPosition.value : props.position;
      console.log(position.value, 'position.value');
      return {
        position: 'fixed' as const,
        left: `${position.value.x}px`,
        top: `${position.value.y}px`,
        zIndex: 6000
      };
    });

    const show = (options: ContextMenuOptions) => {
      contextMenu.value = options.menus;
      position.value = {
        x: options.x,
        y: options.y
      };
      visible.value = true;
      console.log('show', visible.value, position.value, contextMenu.value);
      emit('show');
    };

    const hide = () => {
      visible.value = false;
    };

    // 菜单隐藏前的处理
    const onBeforeHide = () => {
      // 可以在这里添加清理逻辑
      contextMenu.value = [];
      position.value = { x: 0, y: 0 };
      visible.value = false;

      emit('hide');
    };

    expose({
      show,
      hide
    });

    return {
      visible,
      position,
      contextMenu,
      menuStyle,
      show,
      hide,
      onBeforeHide
    };
  }
});
