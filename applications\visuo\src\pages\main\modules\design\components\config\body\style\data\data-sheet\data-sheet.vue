<template>
  <q-dialog
    :class="{ fullscreen: isFullscreen }"
    transition-show="seamless"
    transition-hide="seamless"
    @hide="handleClose"
  >
    <div
      class="vis-data-sheet"
      :style="{ borderRadius: isFullscreen ? '0' : '8px' }"
      tabindex="0"
      @keydown.stop="handleUnifiedKeydown"
      @click="handleMainContainerClick"
    >
      <!-- 提示信息 -->
      <div v-if="pasteTip || performanceTip || fullscreenTip" class="vis-data-sheet__tips">
        <div v-if="pasteTip" class="vis-data-sheet__tip vis-data-sheet__tip--warning">
          {{ pasteTip }}
        </div>
        <div v-if="performanceTip" class="vis-data-sheet__tip vis-data-sheet__tip--info">
          {{ performanceTip }}
        </div>
        <div v-if="fullscreenTip" class="vis-data-sheet__tip vis-data-sheet__tip--success">
          {{ fullscreenTip }}
        </div>
      </div>

      <div class="vis-data-sheet__header h-10 items-center justify-between">
        <div class="vis-data-sheet__title px-3 font-bold">编辑数据</div>

        <!-- 工具栏 -->
        <div class="vis-data-sheet__toolbar">
          <div class="vis-data-sheet__toolbar-right px-3">
            <!-- 冻结功能按钮 
            <q-btn
              @click="toggleFrozenHeader"
              :ripple="false"
              :class="{ active: frozenHeader }"
              :title="frozenHeader ? '取消冻结表头' : '冻结表头'"
            >
              <q-icon name="view_column" />
              <q-tooltip>{{ frozenHeader ? '取消冻结表头' : '冻结表头' }}</q-tooltip>
            </q-btn>
            <q-btn
              @click="toggleFrozenRowHeader"
              :ripple="false"
              :class="{ active: frozenRowHeader }"
              :title="frozenRowHeader ? '取消冻结行号' : '冻结行号'"
            >
              <q-icon name="view_column" />
              <q-tooltip>{{ frozenRowHeader ? '取消冻结行号' : '冻结行号' }}</q-tooltip>
            </q-btn>
            <q-separator vertical />
            -->

            <!-- 行添加按钮 -->
            <q-btn @click="addRow" :disable="!isCurrentSheetEditable" title="添加行">
              <q-icon name="add" />
              <q-tooltip>{{ isCurrentSheetEditable() ? '添加行' : '内置数据禁止编辑' }}</q-tooltip>
            </q-btn>

            <!-- 列添加按钮 -->
            <q-btn @click="addColumn" :disable="!isCurrentSheetEditable" title="添加列">
              <q-icon name="add" />
              <q-tooltip>{{ isCurrentSheetEditable() ? '添加列' : '内置数据禁止编辑' }}</q-tooltip>
            </q-btn>
            <q-separator vertical />

            <q-btn @click="undo" :disable="!canUndo || !isCurrentSheetEditable" title="撤销">
              <q-icon name="undo" />
              <q-tooltip>{{ isCurrentSheetEditable() ? '撤销' : '内置数据禁止编辑' }}</q-tooltip>
            </q-btn>
            <q-btn @click="redo" :disable="!canRedo || !isCurrentSheetEditable" title="重做">
              <q-icon name="redo" />
              <q-tooltip>{{ isCurrentSheetEditable() ? '重做' : '内置数据禁止编辑' }}</q-tooltip>
            </q-btn>
            <q-separator vertical />
            <q-btn @click="copySelected" :disable="!hasSelection" title="复制">
              <q-icon name="content_copy" />
              <q-tooltip>复制</q-tooltip>
            </q-btn>
            <q-btn @click="pasteData()" :disable="readonly || !isCurrentSheetEditable" title="粘贴">
              <q-icon name="content_paste" />
              <q-tooltip>{{ isCurrentSheetEditable() ? '粘贴' : '内置数据禁止编辑' }}</q-tooltip>
            </q-btn>
            <q-separator vertical />
            <q-btn @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '全屏'">
              <q-icon :name="isFullscreen ? 'fullscreen_exit' : 'fullscreen'" />
              <q-tooltip>{{ isFullscreen ? '退出全屏' : '全屏' }}</q-tooltip>
            </q-btn>
            <q-btn title="关闭" v-close-popup>
              <q-icon name="close" />
              <q-tooltip>关闭</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>

      <!-- 表格容器 -->
      <div
        class="vis-data-sheet__container-wrapper"
        tabindex="0"
        @keydown.stop="handleUnifiedKeydown"
        @click="ensureContainerWrapperFocus"
      >
        <q-scroll-area
          class="vis-data-sheet__container"
          :style="
            isFullscreen ? 'height: calc(100vh - 85px); width: calc(100vw - 2px)' : 'height: 60vh; width: calc(75vw)'
          "
        >
          <!-- 列宽调整线 -->
          <div
            v-if="resizeLine.visible"
            class="vis-data-sheet__resize-line"
            :style="{ left: resizeLine.x + 'px' }"
          ></div>
          <!-- 列标题行 -->
          <div class="vis-data-sheet__header" :style="getFrozenHeaderStyle()">
            <div class="vis-data-sheet__corner" :style="getFrozenCornerStyle()"></div>
            <div
              v-for="(col, colIndex) in columnHeaders"
              :key="colIndex"
              class="vis-data-sheet__header-cell"
              :class="{ 'vis-data-sheet__header-cell--selected': isColumnSelected(colIndex) }"
              :style="{ width: getColumnWidth(colIndex) + 'px' }"
              @click="selectColumn(colIndex, $event)"
            >
              <div class="vis-data-sheet__header-content">
                <span class="vis-data-sheet__header-text">{{ col.name }}</span>
                <!-- 字段类型选择器 -->
                <div class="vis-data-sheet__type-selector">
                  <q-btn @click.stop="startEditColumnType(colIndex)" :ripple="false">
                    <ht-icon :name="`vis-${getDataTypeIcon(col.type)}`" class="vis-icon" />
                    <q-tooltip v-if="isCurrentSheetEditable()">修改字段类型</q-tooltip>
                    <!-- 类型选择下拉菜单 -->
                    <q-menu
                      v-if="isCurrentSheetEditable()"
                      class="vis-menu"
                      @hide="finishEditColumnType"
                      anchor="bottom left"
                      self="top left"
                    >
                      <q-list class="vis-list">
                        <q-item
                          clickable
                          @click="changeColumnType(colIndex, FieldType.STRING)"
                          :active="col.type === FieldType.STRING"
                          v-close-popup
                        >
                          <ht-icon name="vis-field-string" class="mr-2" />
                          字符
                        </q-item>
                        <q-item
                          clickable
                          @click="changeColumnType(colIndex, FieldType.NUMBER)"
                          :active="col.type === FieldType.NUMBER"
                          v-close-popup
                        >
                          <ht-icon name="vis-field-integer" class="mr-2" />
                          数字
                        </q-item>
                        <q-item
                          clickable
                          @click="changeColumnType(colIndex, FieldType.DATE)"
                          :active="col.type === FieldType.DATE"
                          v-close-popup
                        >
                          <ht-icon name="vis-field-date" class="mr-2" />
                          日期
                        </q-item>
                        <q-separator />
                        <q-item clickable @click="deleteColumn(colIndex)" v-close-popup>
                          <ht-icon name="vis-delete" class="mr-2" />
                          删除
                        </q-item>
                      </q-list>
                    </q-menu>
                  </q-btn>
                </div>
              </div>
              <!-- 列宽调整分割线 -->
              <div class="vis-data-sheet__resize-handle" @mousedown="startResizeColumn($event, colIndex)"></div>
            </div>
          </div>

          <div class="vis-data-sheet__body">
            <div v-for="(row, rowIndex) in data" :key="rowIndex" class="vis-data-sheet__row">
              <!-- 行标题 -->
              <div
                class="vis-data-sheet__row-header"
                :class="{ 'vis-data-sheet__row-header--selected': isRowSelected(rowIndex) }"
                :style="getFrozenRowHeaderStyle()"
                @click="selectRow(rowIndex, $event)"
              >
                {{ rowIndex + 1 }}
                <q-menu v-if="isCurrentSheetEditable()" class="vis-menu" context-menu>
                  <q-item
                    class="!my-0 !py-1"
                    clickable
                    @click.stop="deleteRow(rowIndex)"
                    v-close-popup
                    :disable="!isCurrentSheetEditable"
                  >
                    <ht-icon name="vis-delete" class="vis-icon mr-1" />
                    {{ isCurrentSheetEditable() ? '删除' : '内置数据禁止编辑' }}
                  </q-item>
                </q-menu>
              </div>

              <!-- 数据单元格 -->
              <div
                v-for="(cell, colIndex) in row"
                :key="colIndex"
                class="vis-data-sheet__cell"
                :class="{
                  'vis-data-sheet__cell--selected': isCellSelected(rowIndex, colIndex),
                  'vis-data-sheet__cell--editing': isEditing(rowIndex, colIndex),
                  'vis-data-sheet__cell--focused': isFocused(rowIndex, colIndex),
                  'multi-selection': hasMultiSelection && isCellSelected(rowIndex, colIndex),
                  'vis-data-sheet__cell--readonly': !isCurrentSheetEditable
                }"
                :style="{ width: getColumnWidth(colIndex) + 'px' }"
                :data-row="rowIndex"
                :data-col="colIndex"
                @click="selectCell(rowIndex, colIndex, $event)"
                @mousedown="startSelection(rowIndex, colIndex, $event)"
                @dblclick="startEdit(rowIndex, colIndex, cell)"
                @focus="onCellFocus(rowIndex, colIndex)"
                :tabindex="isCurrentSheetEditable() ? '0' : '-1'"
              >
                <div v-if="!isEditing(rowIndex, colIndex)" class="vis-data-sheet__cell-content">
                  {{ formatCellValue(cell) }}
                </div>
                <input
                  v-else
                  v-model="editValue"
                  class="vis-data-sheet__edit-input"
                  @blur="finishEdit"
                  @keydown.enter="handleEditKeydown"
                  @keydown.tab="handleEditKeydown"
                  @keydown.esc="cancelEdit"
                  @keydown.stop
                />
              </div>
            </div>
          </div>
        </q-scroll-area>
      </div>

      <!-- Sheet 标签页 -->
      <div class="vis-data-sheet__tabs">
        <q-scroll-area class="vis-data-sheet__tabs-container" style="min-width: 24px; height: 28px">
          <div
            v-for="(sheet, index) in sheets"
            :key="index"
            class="vis-data-sheet__tab"
            :class="{
              'vis-data-sheet__tab--active': index === currentSheetIndex,
              'vis-data-sheet__tab--renaming': isRenamingSheet(index),
              'vis-data-sheet__tab--readonly': !isCurrentSheetEditable(index)
            }"
            @click="switchSheet(index)"
            @dblclick="startRenameSheet(index)"
          >
            <span v-if="!isRenamingSheet(index)" class="vis-data-sheet__tab-text">
              {{ !isCurrentSheetEditable(index) ? ' 内置数据(只读)' : sheet.name }}
            </span>
            <input
              v-else
              v-model="sheetRenameValue"
              class="vis-data-sheet__tab-rename-input"
              @blur="finishRenameSheet"
              @keydown.enter="finishRenameSheet"
              @keydown.esc="cancelRenameSheet"
              ref="renameInput"
            />
            <q-btn
              v-if="sheets.length > 1 && isCurrentSheetEditable(index)"
              flat
              round
              dense
              icon="close"
              size="xs"
              class="vis-data-sheet__tab-close"
              @click.stop="deleteSheet(index)"
            >
              <q-tooltip>删除sheet页</q-tooltip>
            </q-btn>
          </div>
          <!-- 添加按钮 -->
          <div class="vis-data-sheet__add-sheet">
            <q-btn :ripple="false" icon="add" @click="addSheet" title="添加sheet页" />
          </div>
        </q-scroll-area>
      </div>
    </div>
  </q-dialog>
</template>
<script lang="ts" src="./data-sheet.ts"></script>
<style lang="scss" src="./data-sheet.scss" />
