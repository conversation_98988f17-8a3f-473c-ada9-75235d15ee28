<template>
  <div class="vis-tab h-full overflow-hidden">
    <q-scroll-area
      v-if="
        layout.scrollbar && (tabOption.resizeX === TabResizeType.Fixed || tabOption.resizeY === TabResizeType.Fixed)
      "
      class="fit"
    >
      <div :style="tabStyle">
        <div
          v-for="(item, idx) in data"
          :key="item.id"
          :style="tabItemStyle(idx)"
          class="vis-tab__item position-relative overflow-hidden cursor-pointer"
          ref="tabRef"
          @mouseover="hoverIndex = idx"
          @mouseleave="hoverIndex = -1"
          @click="handleActive(idx)"
        >
          <template v-if="shouldShowIcon(idx, 'before')">
            <!-- 悬浮状态下的图标 -->
            <div v-if="hoverIconUrl(idx)" class="overflow-hidden !rounded-none">
              <q-img :style="getHoverIconStyle" :src="hoverIconUrl(idx)"></q-img>
            </div>
            <!-- 选中状态下的图标 -->
            <div v-else-if="activeIconUrl(idx)" class="overflow-hidden !rounded-none">
              <q-img :style="getActiveIconStyle" :src="activeIconUrl(idx)"></q-img>
            </div>
            <!-- 普通状态下的图标 -->
            <template v-else-if="tabOption.icon && tabOption.icon.image">
              <div v-if="tabOption.icon.image.url" class="overflow-hidden !rounded-none">
                <q-img :style="getIconStyle" :src="iconUrl"></q-img>
              </div>
              <div v-else class="overflow-hidden !rounded-none">
                <q-icon
                  name="image"
                  :style="getIconStyle"
                  :size="tabOption.icon.width + 'px'"
                  class="text-grey-5 text-shadow-none inline-block"
                ></q-icon>
              </div>
            </template>
          </template>
          <div
            class="overflow-hidden vis-tab__text position-relative"
            :style="tabContentStyle(idx)"
            ref="tabContentRef"
          >
            <span :style="getItemOverflowStyle(idx)" ref="marqueeRefs">{{ item.text }}</span>
            <!-- 用于检测换行时，文本是否处于溢出状态 -->
            <span
              v-if="tabOption.style.overflow === 2"
              class="position-absolute top-0 left-0 opacity-0 whitespace-nowrap"
              ref="changLineRefs"
              >{{ item.text }}</span
            >
            <template v-if="tabOption.style.overflow === 3 && textOverflowFlags[idx]">
              <span :style="getItemOverflowStyle(idx)">{{ item.text }}</span>
              <span :style="getItemOverflowStyle(idx)">{{ item.text }}</span>
            </template>
          </div>
          <template v-if="shouldShowIcon(idx, 'after')">
            <!-- 悬浮状态下的图标 -->
            <div v-if="hoverIconUrl(idx)" class="overflow-hidden !rounded-none">
              <q-img :style="getHoverIconStyle" :src="hoverIconUrl(idx)"></q-img>
            </div>
            <!-- 选中状态下的图标 -->
            <div v-else-if="activeIconUrl(idx)" class="overflow-hidden !rounded-none">
              <q-img :style="getActiveIconStyle" :src="activeIconUrl(idx)"></q-img>
            </div>
            <!-- 普通状态下的图标 -->
            <template v-else-if="tabOption.icon && tabOption.icon.image">
              <div v-if="tabOption.icon.image.url" class="overflow-hidden !rounded-none">
                <q-img :style="getIconStyle" :src="iconUrl"></q-img>
              </div>
              <div v-else class="overflow-hidden !rounded-none">
                <q-icon
                  name="image"
                  :style="getIconStyle"
                  :size="tabOption.icon.width + 'px'"
                  class="text-grey-5 text-shadow-none inline-block"
                ></q-icon>
              </div>
            </template>
          </template>
        </div>
      </div>
    </q-scroll-area>
    <div class="h-full" v-else :style="tabStyle">
      <div
        v-for="(item, idx) in data"
        :key="item.id"
        :style="tabItemStyle(idx)"
        class="vis-tab__item position-relative overflow-hidden cursor-pointer"
        ref="tabRef"
        @mouseover="hoverIndex = idx"
        @mouseleave="hoverIndex = -1"
        @click="handleActive(idx)"
      >
        <template v-if="shouldShowIcon(idx, 'before')">
          <!-- 悬浮状态下的图标 -->
          <div v-if="hoverIconUrl(idx)" class="overflow-hidden !rounded-none">
            <q-img :style="getHoverIconStyle" :src="hoverIconUrl(idx)"></q-img>
          </div>
          <!-- 选中状态下的图标 -->
          <div v-else-if="activeIconUrl(idx)" class="overflow-hidden !rounded-none">
            <q-img :style="getActiveIconStyle" :src="activeIconUrl(idx)"></q-img>
          </div>
          <!-- 普通状态下的图标 -->
          <template v-else-if="tabOption.icon && tabOption.icon.image">
            <div v-if="tabOption.icon.image.url" class="overflow-hidden !rounded-none">
              <q-img :style="getIconStyle" :src="iconUrl"></q-img>
            </div>
            <div v-else class="overflow-hidden !rounded-none">
              <q-icon
                name="image"
                :style="getIconStyle"
                :size="tabOption.icon.width + 'px'"
                class="text-grey-5 text-shadow-none inline-block"
              ></q-icon>
            </div>
          </template>
        </template>
        <div class="overflow-hidden vis-tab__text position-relative" :style="tabContentStyle(idx)" ref="tabContentRef">
          <span :style="getItemOverflowStyle(idx)" ref="marqueeRefs">{{ item.text }}</span>
          <!-- 用于检测换行时，文本是否处于溢出状态 -->
          <span
            v-if="tabOption.style.overflow === 2"
            class="position-absolute top-0 left-0 opacity-0 whitespace-nowrap"
            ref="changLineRefs"
            >{{ item.text }}</span
          >
          <template v-if="tabOption.style.overflow === 3 && textOverflowFlags[idx]">
            <span :style="getItemOverflowStyle(idx)">{{ item.text }}</span>
            <span :style="getItemOverflowStyle(idx)">{{ item.text }}</span>
          </template>
        </div>
        <template v-if="shouldShowIcon(idx, 'after')">
          <!-- 悬浮状态下的图标 -->
          <div v-if="hoverIconUrl(idx)" class="overflow-hidden !rounded-none">
            <q-img :style="getHoverIconStyle" :src="hoverIconUrl(idx)"></q-img>
          </div>
          <!-- 选中状态下的图标 -->
          <div v-else-if="activeIconUrl(idx)" class="overflow-hidden !rounded-none">
            <q-img :style="getActiveIconStyle" :src="activeIconUrl(idx)"></q-img>
          </div>
          <!-- 普通状态下的图标 -->
          <template v-else-if="tabOption.icon && tabOption.icon.image">
            <div v-if="tabOption.icon.image.url" class="overflow-hidden !rounded-none">
              <q-img :style="getIconStyle" :src="iconUrl"></q-img>
            </div>
            <div v-else class="overflow-hidden !rounded-none">
              <q-icon
                name="image"
                :style="getIconStyle"
                :size="tabOption.icon.width + 'px'"
                class="text-grey-5 text-shadow-none inline-block"
              ></q-icon>
            </div>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./tab.ts"></script>
<style lang="scss" src="./tab.scss"></style>
