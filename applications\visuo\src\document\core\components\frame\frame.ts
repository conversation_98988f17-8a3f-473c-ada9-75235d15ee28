import type { QS<PERSON><PERSON><PERSON><PERSON> } from 'quasar';
import { BehaviorType, DirectionType, Frame } from '../../models';
import { defineComponent, ref, computed, type PropType } from 'vue';
import { useGraphStyle, useLayout } from '../../hooks';
import { useDocumentStore } from '../../stores';
import type { Records } from '@hetu/util';

/**
 * 预览/发布容器组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-frame',
  components: {},
  props: {
    graph: {
      type: Object as PropType<Frame>,
      required: true
    },
    parent: {
      type: Object as PropType<Frame>,
      required: false
    }
  },
  setup(props) {
    const docStore = useDocumentStore();
    const {
      isFreeform,
      isFlex,
      isGrid,
      freeformStyle,
      frameFlexStyle,
      flexItemStyle,
      frameGridStyle,
      constraintsStyle,
      frameWrapStyle,
      gridItemStyle
    } = useLayout();
    const { graphClassName, graphBaseStyle } = useGraphStyle();

    const frame = computed(() => props.graph);
    const parent = computed(() => props.parent);
    const mainFrame = computed(() => docStore.mainFrame.value);
    // 是否是主容器
    const isMainFrame = computed(() => mainFrame.value.id === frame.value.id);

    const className = computed(() => {
      return graphClassName(frame.value).join(' ');
    });

    const frameStyle = computed(() => {
      const getFrameStyle = (frame: Frame, parent?: Frame) => {
        const { width, height } = frame;
        // 基础样式
        const baseStyle = graphBaseStyle(frame);

        let positionStyle: Records<string | number> = {};

        if (isFlex(frame)) {
          positionStyle = frameFlexStyle(frame);
        } else if (isGrid(frame)) {
          positionStyle = frameGridStyle(frame);
        } else if (isFreeform(frame)) {
          positionStyle = freeformStyle(frame);
        }

        // 说明frame是容器下的子元素
        if (parent) {
          // 父级是自由布局
          if (isFreeform(parent)) {
            // 处理设置约束时的位置
            if (frame.constraints) {
              positionStyle = constraintsStyle(frame, parent);
            }
          }
          // 父级是flex布局
          else if (isFlex(parent)) {
            // 忽略自动布局时，按约束布局
            if (frame.ignoreAutoLayout && frame.constraints) {
              positionStyle = constraintsStyle(frame, parent);
            } else {
              positionStyle = flexItemStyle(frame, parent);
            }
          }
          // 父级是grid布局
          else if (isGrid(parent)) {
            if (frame.ignoreAutoLayout && frame.constraints) {
              positionStyle = constraintsStyle(frame, parent);
            } else {
              positionStyle = gridItemStyle(frame, parent);
            }
          }
        }

        // 主容器 不设置transform
        if (isMainFrame.value) {
          delete positionStyle.transform;
          delete positionStyle.transformOrigin;
        }

        // 顶级容器，设置固定布局和响应式布局
        if (!frame.parent) {
          if (frame.autoLayout.type === 'responsive') {
            positionStyle.width = '100%';
            positionStyle.height = '100%';
            delete positionStyle.position;
          } else {
            positionStyle.width = `${width}px`;
            positionStyle.height = `${height}px`;
          }
        }

        return { ...positionStyle, ...baseStyle };
      };
      return getFrameStyle(frame.value, parent.value);
    });

    const wrapStyle = computed(() => {
      const style = frameWrapStyle(frame.value);
      const { width, height } = frame.value;
      // 有滚动条的情况删除掉overflow属性
      if (isScroll.value) {
        delete style.overflow;
        delete style.position;
      }

      // 顶级容器，设置固定布局和响应式布局
      if (!frame.value.parent) {
        if (frame.value.autoLayout.type === 'responsive') {
          style.width = '100%';
          style.height = isFreeform(frame.value) ? '100%' : 'auto';
          delete style.position;

          // grid布局时，网格均分时宽或高为100%，否则为auto
          if (isGrid(frame.value)) {
            const { gridRowsSizing, gridColumnsSizing } = frame.value.autoLayout;
            if (new Set(gridRowsSizing).size === 1 && gridRowsSizing[0] === 'Auto') {
              style.height = '100%';
            } else {
              style.height = 'auto';
            }
            if (new Set(gridColumnsSizing).size === 1 && gridColumnsSizing[0] === 'Auto') {
              style.width = '100%';
            } else {
              style.width = 'auto';
            }
          }
        } else {
          style.width = `${width}px`;
          style.height = `${height}px`;
        }
      }

      return style;
    });

    // 是否有滚动条
    const isScroll = computed(() => frame.value.autoLayout.scrollDirection !== 'none');

    // 容器样式
    const scrollStyle = computed(() => {
      // 主容器的滚动容器占全屏
      if (isMainFrame.value) {
        return {
          position: 'absolute',
          width: '100%',
          height: '100%'
        };
      } else {
        return {
          position: 'absolute',
          width: frame.value.width + 'px',
          height: frame.value.height + 'px'
        };
      }
    });

    // 正常渲染的下级图形 1. 固定布局时，正常渲染子图形是随父级滚动的图形 2. 自动布局时，不包括忽略自动布局的元素
    const children = computed(() => {
      return frame.value.children.filter((g) => {
        let flag = !g.constraints || g.constraints.scrollBehavior === BehaviorType.Follow;
        if (frame.value.autoLayout.direction !== DirectionType.Freeform) {
          flag = !g.ignoreAutoLayout;
        }
        return flag;
      });
    });

    // 设置了固定行为是“固定”的下级
    // 1. 固定布局：容器滚动时，设置了“固定”和“吸顶固定”图形会跟随滚动，所以将这两类图形放到与滚动容器平级，
    // 2. 自动布局：包括忽略自动布局的元素
    const fixChildren = computed(() => {
      return frame.value.children.filter((g) => {
        let flag = g.constraints && g.constraints.scrollBehavior !== BehaviorType.Follow;
        if (frame.value.autoLayout.direction !== DirectionType.Freeform) {
          flag = g.ignoreAutoLayout;
        }
        return flag;
      });
    });

    // 设置了固定行为是“吸顶固定”的下级
    const stickyChildren = computed(() => {
      return frame.value.children.filter((g) => g.constraints && g.constraints.scrollBehavior === BehaviorType.Sticky);
    });

    const scrollRef = ref<QScrollArea>();
    const onScroll = () => {
      // 当容器滚动时，设置“吸顶固定”图形的top值,使得图形随着容器滚动，当滚动位置在“吸顶固定”图形的顶端时，固定在容器顶部，达到吸顶的效果
      if (stickyChildren.value.length) {
        const scrollPosition = scrollRef.value?.getScrollPosition();
        const scrollTop = scrollPosition?.top || 0;
        const scrollLeft = scrollPosition?.left || 0;
        if (scrollTop || scrollLeft) {
          stickyChildren.value.forEach((g) => {
            const x = g.transform.translate[0];
            const y = g.transform.translate[1];
            const top = y > scrollTop ? y - scrollTop : 0;
            const left = x > scrollLeft ? x - scrollLeft : 0;
            const gRef = document.querySelector(`[id="${g.id}"]`) as HTMLElement;
            if (gRef) {
              gRef.style.left = `${left}px`;
              gRef.style.top = `${top}px`;
              console.log(top);
            }
          });
        }
      }
    };

    //#region 计算空容器的宽度、高度
    const contentStyle = ref({});

    if (frame.value.autoLayout.scrollDirection !== 'none') {
      const widths: number[] = [];
      const heights: number[] = [];
      children.value.forEach((g) => {
        widths.push(g.width + g.transform.translate[0]);
        heights.push(g.height + g.transform.translate[1]);
      });

      contentStyle.value = {
        width: `${Math.max(...widths)}px`,
        height: `${Math.max(...heights)}px`
      };
    }
    //#endregion

    return {
      frame,
      children,
      fixChildren,
      stickyChildren,

      className,
      frameStyle,
      wrapStyle,

      isFlex,
      isGrid,
      isMainFrame,

      isScroll,
      scrollRef,
      scrollStyle,
      contentStyle,

      onScroll
    };
  }
});
