import { defineComponent, ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue';
import { DocumentService } from '@vis/document-core';

// 字段类型枚举
export enum FieldType {
  STRING = 'string',
  NUMBER = 'number',
  DATE = 'date'
}

// 列信息
export interface ColumnInfo {
  name: string;
  type: FieldType;
}

// Sheet页
export interface DataSheet {
  name: string;
  data: any[][];
  columnHeaders: ColumnInfo[];
  columnWidths: { [key: number]: number };
  selection: { startRow: number; startCol: number; endRow: number; endCol: number };
}

// 选择范围接口
interface SelectionRange {
  minRow: number;
  maxRow: number;
  minCol: number;
  maxCol: number;
}

// 多区域选择接口
interface MultiSelection {
  ranges: SelectionRange[];
  activeRange: number; // 当前激活的区域索引
}

/**
 * 数据编辑表格
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-data-sheet',
  props: {
    modelValue: {
      type: Object as () => {},
      default: () => ({})
    },
    columns: {
      type: Array as () => string[],
      default: () => []
    },
    rows: {
      type: Number,
      default: 10
    },
    cols: {
      type: Number,
      default: 10
    },
    showToolbar: {
      type: Boolean,
      default: true
    },
    readonly: {
      type: Boolean,
      default: false
    },
    cellWidth: {
      type: Number,
      default: 120
    },
    cellHeight: {
      type: Number,
      default: 32
    },
    headerHeight: {
      type: Number,
      default: 40
    },
    rowHeaderWidth: {
      type: Number,
      default: 60
    }
  },
  emits: ['update:modelValue', 'cell-change', 'selection-change', 'close', 'formatted-data-change'],
  setup(props, { emit }) {
    // Sheet页管理
    const sheets = ref<DataSheet[]>([]);
    const currentSheetIndex = ref(0);
    const renamingSheetIndex = ref(-1);
    const sheetRenameValue = ref('');

    const currentSheet = computed(() => sheets.value[currentSheetIndex.value]);

    // 判断当前sheet是否可编辑（内置数据禁止编辑）
    const isCurrentSheetEditable = (index: number = currentSheetIndex.value) => {
      return sheets.value[index].name !== 'STATIC_DATA';
    };

    const data = computed({
      get: () => currentSheet.value?.data || [],
      set: (value) => {
        if (currentSheet.value && isCurrentSheetEditable(currentSheetIndex.value)) {
          currentSheet.value.data = value;
        }
      }
    });

    const columnHeaders = computed({
      get: () => currentSheet.value?.columnHeaders || [],
      set: (value) => {
        if (currentSheet.value) {
          currentSheet.value.columnHeaders = value;
        }
      }
    });

    const selection = computed({
      get: () => currentSheet.value?.selection || { startRow: -1, startCol: -1, endRow: -1, endCol: -1 },
      set: (value) => {
        if (currentSheet.value) {
          currentSheet.value.selection = value;
        }
      }
    });

    const editingCell = ref<{ row: number; col: number } | null>(null);
    const editValue = ref<string>('');

    // 冻结功能状态
    const frozenHeader = ref(true); // 冻结列标题行
    const frozenRowHeader = ref(true); // 冻结行标题列

    // 多选相关状态
    const isSelecting = ref(false);
    const selectionStart = ref<{ row: number; col: number } | null>(null);
    const multiSelection = ref<MultiSelection>({
      ranges: [],
      activeRange: -1
    });

    // 全屏状态
    const isFullscreen = ref(false);

    // 字段类型管理
    const editingColumnType = ref<number | null>(null);

    // 基础变量
    const rowHeight = props.cellHeight;
    const scrollTop = ref(0);

    // 获取正确的滚动容器的辅助函数
    const getScrollContainer = (): HTMLElement | null => {
      const container = document.querySelector('.vis-data-sheet__container') as HTMLElement;
      if (!container) return null;

      // 尝试获取滚动容器，优先使用 .scroll，然后是 .scroll-content
      let scrollContainer = document.querySelector('.vis-data-sheet__container .scroll') as HTMLElement;
      if (!scrollContainer) {
        scrollContainer = document.querySelector('.vis-data-sheet__container .scroll-content') as HTMLElement;
      }
      if (!scrollContainer) {
        scrollContainer = container;
      }

      return scrollContainer;
    };

    let scrollTimeout: number | null = null;
    const handleScroll = (event: Event) => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      scrollTimeout = window.setTimeout(() => {
        const target = event.target as HTMLElement;
        scrollTop.value = target.scrollTop;

        // 在滚动时重新验证选择范围的有效性
        if (hasSelection.value) {
          const range = getSelectionRange();
          if (range) {
            // 确保选择范围在有效的数据范围内
            const maxRowIndex = data.value.length - 1;
            const maxColIndex = columnHeaders.value.length - 1;

            if (range.maxRow > maxRowIndex || range.maxCol > maxColIndex) {
              // 如果选择范围超出数据范围，调整选择范围
              selection.value = {
                startRow: Math.min(selection.value.startRow, maxRowIndex),
                startCol: Math.min(selection.value.startCol, maxColIndex),
                endRow: Math.min(selection.value.endRow, maxRowIndex),
                endCol: Math.min(selection.value.endCol, maxColIndex)
              };
            }
          }
        }
      }, 16);
    };

    const handleScrollImmediate = (event: Event) => {
      const target = event.target as HTMLElement;
      scrollTop.value = target.scrollTop;
    };

    // 确保行存在
    const ensureRowExists = (rowIndex: number) => {
      while (data.value.length <= rowIndex) {
        const newRow: any[] = [];
        for (let j = 0; j < columnHeaders.value.length; j++) {
          newRow.push('');
        }
        data.value.push(newRow);
      }
    };

    // 确保列存在
    const ensureColumnExists = (colIndex: number) => {
      while (columnHeaders.value.length <= colIndex) {
        const newColIndex = columnHeaders.value.length;
        columnHeaders.value.push({
          name: getColumnLabel(newColIndex),
          type: FieldType.STRING
        });

        // 更新当前sheet页的列宽设置
        if (currentSheet.value) {
          currentSheet.value.columnWidths[newColIndex] = props.cellWidth;
        }

        // 为所有行添加新列
        for (const row of data.value) {
          row.push('');
        }
      }
    };

    // 列宽管理
    const resizingColumn = ref<{
      index: number;
      startX: number;
      startWidth: number;
      currentMouseX: number;
      initialColumnX: number;
    } | null>(null);
    const resizeLine = ref<{ visible: boolean; x: number; columnIndex: number }>({
      visible: false,
      x: 0,
      columnIndex: -1
    });

    // 历史记录
    const history = ref<any[][][]>([]);
    const historyIndex = ref(-1);

    const hasSelection = computed(() => {
      return selection.value.startRow >= 0 && selection.value.startCol >= 0;
    });

    const canUndo = computed(() => historyIndex.value > 0);
    const canRedo = computed(() => historyIndex.value < history.value.length - 1);
    const hasMultiSelection = computed(() => multiSelection.value.ranges.length > 0);

    // 获取冻结样式
    const getFrozenHeaderStyle = () => {
      if (frozenHeader.value) {
        return {
          position: 'sticky' as const,
          top: '0px',
          zIndex: 25
        };
      }
      return {};
    };

    const getFrozenRowHeaderStyle = () => {
      if (frozenRowHeader.value) {
        return {
          position: 'sticky' as const,
          left: '0px',
          zIndex: 15
        };
      }
      return {};
    };

    const getFrozenCornerStyle = () => {
      // 角头在表头或行标题冻结时使用sticky定位
      if (frozenHeader.value || frozenRowHeader.value) {
        const style: any = {
          position: 'sticky' as const,
          zIndex: 30
        };

        // 如果表头冻结，角头固定在顶部
        if (frozenHeader.value) {
          style.top = '0px';
        }

        // 如果行标题冻结，角头固定在左侧
        if (frozenRowHeader.value) {
          style.left = '0px';
        }

        return style;
      }
      return {};
    };

    const getFrozenAddRowStyle = () => {
      if (frozenRowHeader.value) {
        return {
          position: 'sticky' as const,
          left: '0px',
          zIndex: 15
        };
      }
      return {};
    };

    // 格式化数据的计算属性
    const formattedData = computed(() => {
      const result: { [key: string]: any[] } = {};

      sheets.value.forEach((sheet, sheetIndex) => {
        const sheetName = sheet.name || `sheet${sheetIndex + 1}`;
        const formattedRows: any[] = [];

        // 处理每一行数据
        sheet.data.forEach((row, rowIndex) => {
          const formattedRow: { [key: string]: any } = {};

          // 处理每一列数据
          row.forEach((cell, colIndex) => {
            const columnHeader = sheet.columnHeaders[colIndex];
            const columnLabel = columnHeader ? columnHeader.name : getColumnLabel(colIndex);
            formattedRow[columnLabel] = formatCellValue(cell, columnHeader);
          });

          // 只有当行不为空时才添加到结果中
          const hasData = Object.values(formattedRow).some((value) => value !== '');
          if (hasData) {
            formattedRows.push(formattedRow);
          }
        });

        result[sheetName] = formattedRows;
      });

      return result;
    });

    // 工具方法
    const getColumnLabel = (index: number): string => {
      let label = '';
      while (index >= 0) {
        label = String.fromCharCode(65 + (index % 26)) + label;
        index = Math.floor(index / 26) - 1;
      }
      return label;
    };

    const getColumnWidth = (index: number): number => {
      // 使用当前sheet页的列宽设置
      if (currentSheet.value && currentSheet.value.columnWidths) {
        return currentSheet.value.columnWidths[index] || props.cellWidth;
      }
      return props.cellWidth;
    };

    // 获取选择范围边界
    const getSelectionRange = (): SelectionRange | null => {
      if (!hasSelection.value) return null;

      const { startRow, startCol, endRow, endCol } = selection.value;
      return {
        minRow: Math.min(startRow, endRow),
        maxRow: Math.max(startRow, endRow),
        minCol: Math.min(startCol, endCol),
        maxCol: Math.max(startCol, endCol)
      };
    };

    // 检查是否在选择范围内
    const isInSelectionRange = (row: number, col: number, range: SelectionRange): boolean => {
      return row >= range.minRow && row <= range.maxRow && col >= range.minCol && col <= range.maxCol;
    };

    // 检查是否在任何选择范围内
    const isInAnySelectionRange = (row: number, col: number): boolean => {
      // 检查主选择范围
      const mainRange = getSelectionRange();
      if (mainRange && isInSelectionRange(row, col, mainRange)) {
        return true;
      }

      // 检查多选范围
      return multiSelection.value.ranges.some((range) => isInSelectionRange(row, col, range));
    };

    // 更新数据并保存历史记录
    const updateDataAndSave = (newData: any[][]) => {
      data.value = newData;

      emit('update:modelValue', data.value);
      saveToHistory();
    };

    // 重置编辑状态
    const resetEditState = () => {
      editingCell.value = null;
      editValue.value = '';
    };

    // #region ------------------单元格操作方法------------------
    // 格式化单元格值
    const formatCellValue = (cell: any, columnHeader?: ColumnInfo): any => {
      if (cell === null || cell === undefined) return '';

      // 如果提供了列标题配置，根据数据类型进行转换
      if (columnHeader && columnHeader.type) {
        return convertValueByType(cell, columnHeader.type);
      }

      return cell;
    };

    // 检查单元格是否被选中
    const isCellSelected = (row: number, col: number): boolean => {
      return isInAnySelectionRange(row, col);
    };

    // 检查行是否被选中
    const isRowSelected = (row: number): boolean => {
      const mainRange = getSelectionRange();
      if (mainRange && row >= mainRange.minRow && row <= mainRange.maxRow) {
        return true;
      }

      return multiSelection.value.ranges.some((range) => row >= range.minRow && row <= range.maxRow);
    };

    // 检查列是否被选中
    const isColumnSelected = (col: number): boolean => {
      const mainRange = getSelectionRange();
      if (mainRange && col >= mainRange.minCol && col <= mainRange.maxCol) {
        return true;
      }

      return multiSelection.value.ranges.some((range) => col >= range.minCol && col <= range.maxCol);
    };

    // 检查是否正在编辑
    const isEditing = (row: number, col: number): boolean => {
      return editingCell.value?.row === row && editingCell.value?.col === col;
    };

    // 检查是否获得焦点
    const isFocused = (row: number, col: number): boolean => {
      return selection.value.startRow === row && selection.value.startCol === col;
    };

    /**
     * 开始编辑单元格
     * @param row 行索引
     * @param col 列索引
     * @param initialValue 初始值（可选，用于直接输入模式）
     */
    const startEdit = (row: number, col: number, initialValue?: string) => {
      if (props.readonly || !isCurrentSheetEditable(currentSheetIndex.value)) return;

      // 确保行和列存在
      ensureRowExists(row);
      ensureColumnExists(col);

      editingCell.value = { row, col };

      // 如果提供了初始值，使用它；否则使用单元格的当前值
      if (initialValue !== undefined) {
        editValue.value = initialValue;
      } else {
        const currentSheet = sheets.value[currentSheetIndex.value];
        const columnHeader = currentSheet?.columnHeaders[col];
        editValue.value = formatCellValue(data.value[row][col], columnHeader);
      }

      nextTick(() => {
        const editInput = document.querySelector('.vis-data-sheet__edit-input') as HTMLInputElement;
        if (editInput) {
          editInput.focus();
          // 如果有初始值，将光标移到末尾；否则全选
          if (initialValue !== undefined) {
            editInput.setSelectionRange(editInput.value.length, editInput.value.length);
          } else {
            editInput.select();
          }
        }
      });
    };

    const finishEdit = () => {
      if (!editingCell.value) return;

      const { row, col } = editingCell.value;
      const oldValue = data.value[row][col];
      const newValue = editValue.value;

      if (oldValue !== newValue) {
        data.value[row][col] = newValue;
        emit('cell-change', row, col, newValue);
        updateDataAndSave(data.value);
      }

      resetEditState();
    };

    const cancelEdit = () => {
      resetEditState();
    };

    // #endregion ------------------单元格操作方法------------------

    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value;

      // 显示全屏提示
      if (isFullscreen.value) {
        fullscreenTip.value = '按 ESC 键退出全屏';
        setTimeout(() => {
          fullscreenTip.value = '';
        }, 2000);
      }
    };

    // 处理关闭功能
    const handleClose = () => {
      // 如果当前正在编辑，先完成编辑
      if (editingCell.value) {
        finishEdit();
      }

      // 发送关闭事件
      emit('close', getFormattedData());
    };

    // 列宽调整相关方法
    const startResizeColumn = (event: MouseEvent, columnIndex: number) => {
      if (props.readonly) return;

      event.preventDefault();
      event.stopPropagation();

      const startX = event.clientX;
      const startWidth = getColumnWidth(columnIndex);

      // 计算初始位置，确保定位准确
      let initialColumnX = 50; // 行标题宽度
      for (let i = 0; i < columnIndex; i++) {
        initialColumnX += getColumnWidth(i);
      }
      initialColumnX += startWidth; // 列右边界位置

      resizingColumn.value = {
        index: columnIndex,
        startX,
        startWidth,
        currentMouseX: startX,
        initialColumnX // 添加初始列位置
      };

      // 立即显示调整线在正确位置
      resizeLine.value = {
        visible: true,
        x: initialColumnX,
        columnIndex: columnIndex
      };

      document.addEventListener('mousemove', handleResizeColumn);
      document.addEventListener('mouseup', stopResizeColumn);
    };

    const handleResizeColumn = (event: MouseEvent) => {
      if (!resizingColumn.value) return;

      const { index, startX, startWidth, initialColumnX } = resizingColumn.value;
      const deltaX = event.clientX - startX;

      resizingColumn.value.currentMouseX = event.clientX;

      const lineX = initialColumnX + deltaX;

      const minX = 50 + 50;
      const maxX = 200000;
      const clampedX = Math.max(minX, Math.min(maxX, lineX));

      resizeLine.value = {
        visible: true,
        x: clampedX,
        columnIndex: index
      };

      document.body.classList.add('resizing');
    };

    const stopResizeColumn = () => {
      if (resizingColumn.value) {
        const { index, startX, startWidth } = resizingColumn.value;
        // 使用当前鼠标位置计算最终宽度
        const currentMouseX = resizingColumn.value.currentMouseX || startX;
        const deltaX = currentMouseX - startX;
        const newWidth = Math.max(50, startWidth + deltaX);

        // 鼠标释放后统一修改当前sheet页的列宽
        if (currentSheet.value) {
          currentSheet.value.columnWidths[index] = newWidth;
        }
      }

      resizingColumn.value = null;
      resizeLine.value = { visible: false, x: 0, columnIndex: -1 };
      document.removeEventListener('mousemove', handleResizeColumn);
      document.removeEventListener('mouseup', stopResizeColumn);
      document.body.classList.remove('resizing');
    };

    // #region ------------------Sheet页管理方法------------------
    const createSheet = (name: string): DataSheet => {
      const initialData: any[][] = [];
      for (let i = 0; i < props.rows; i++) {
        const row: any[] = [];
        for (let j = 0; j < props.cols; j++) {
          row.push('');
        }
        initialData.push(row);
      }

      const initialColumns: ColumnInfo[] = [];
      const sheetColumnWidths: { [key: number]: number } = {};
      for (let i = 0; i < props.cols; i++) {
        initialColumns.push({
          name: props.columns[i] || getColumnLabel(i),
          type: FieldType.STRING
        });
        sheetColumnWidths[i] = props.cellWidth;
      }

      return {
        name,
        data: initialData,
        columnHeaders: initialColumns,
        columnWidths: sheetColumnWidths,
        selection: { startRow: -1, startCol: -1, endRow: -1, endCol: -1 }
      };
    };

    // 生成唯一的sheet名称
    const generateUniqueSheetName = (baseName: string): string => {
      let name = baseName;
      let counter = 1;

      while (sheets.value.some((sheet) => sheet.name === name)) {
        name = `${baseName}${counter}`;
        counter++;
      }

      return name;
    };

    const addSheet = () => {
      const sheetCount = sheets.value.length + 1;
      const baseName = `Sheet${sheetCount}`;
      const uniqueName = generateUniqueSheetName(baseName);
      const newSheet = createSheet(uniqueName);
      sheets.value.push(newSheet);
      switchSheet(sheets.value.length - 1);
    };

    const switchSheet = (index: number) => {
      if (index >= 0 && index < sheets.value.length) {
        currentSheetIndex.value = index;
        resetEditState();
        saveToHistory();
      }
    };

    const deleteSheet = (index: number) => {
      if (sheets.value.length <= 1 || !isCurrentSheetEditable(index)) return;

      sheets.value.splice(index, 1);

      if (currentSheetIndex.value >= sheets.value.length) {
        currentSheetIndex.value = sheets.value.length - 1;
      }

      resetEditState();
    };

    // Sheet重命名相关方法
    const startRenameSheet = (index: number) => {
      if (props.readonly || !isCurrentSheetEditable(index)) return;

      renamingSheetIndex.value = index;
      sheetRenameValue.value = sheets.value[index].name;

      nextTick(() => {
        const renameInput = document.querySelector('.vis-data-sheet__tab-rename-input') as HTMLInputElement;
        if (renameInput) {
          renameInput.focus();
        }
      });
    };

    // 检查当前重命名的名称是否重复
    const isRenameDuplicate = computed(() => {
      if (renamingSheetIndex.value < 0) return false;

      const currentName = sheetRenameValue.value.trim();
      if (!currentName) return false;

      return sheets.value.some((sheet, index) => index !== renamingSheetIndex.value && sheet.name === currentName);
    });

    const finishRenameSheet = () => {
      if (renamingSheetIndex.value >= 0) {
        const newName = sheetRenameValue.value.trim();
        if (newName) {
          if (isRenameDuplicate.value) {
            sheetRenameValue.value = sheets.value[renamingSheetIndex.value].name;
            return;
          }

          sheets.value[renamingSheetIndex.value].name = newName;
        }
        renamingSheetIndex.value = -1;
        sheetRenameValue.value = '';
      }
    };

    const cancelRenameSheet = () => {
      renamingSheetIndex.value = -1;
      sheetRenameValue.value = '';
    };

    const isRenamingSheet = (index: number): boolean => {
      return renamingSheetIndex.value === index;
    };

    // #endregion ------------------Sheet页管理方法------------------

    // #region ------------------选择相关方法------------------
    const startSelection = (row: number, col: number, event: MouseEvent) => {
      if (props.readonly) return;

      // 确保行和列存在
      ensureRowExists(row);
      ensureColumnExists(col);

      // 检查是否是多选拖拽（Ctrl/Cmd + 拖拽）
      const isMultiSelectDrag = event.ctrlKey || event.metaKey;

      isSelecting.value = true;
      selectionStart.value = { row, col };

      // 如果不是多选拖拽，清除之前的多选状态
      if (!isMultiSelectDrag) {
        multiSelection.value.ranges = [];
        multiSelection.value.activeRange = -1;
      } else {
        // 如果是多选拖拽，将当前选择添加到多选范围
        if (hasSelection.value) {
          const currentRange = getSelectionRange();
          if (currentRange) {
            multiSelection.value.ranges.push(currentRange);
          }
        }
      }

      // 设置新的选择起始点
      selection.value = { startRow: row, startCol: col, endRow: row, endCol: col };

      // 添加全局鼠标事件监听
      document.addEventListener('mousemove', handleSelectionMove);
      document.addEventListener('mouseup', stopSelection);

      // 确保容器获得焦点，以便接收键盘事件
      nextTick(() => {
        const container = document.querySelector('.vis-data-sheet__container-wrapper') as HTMLElement;
        if (container) {
          container.focus();
        }
      });

      event.preventDefault();
    };

    const handleSelectionMove = (event: MouseEvent) => {
      if (!isSelecting.value || !selectionStart.value) return;

      // 使用元素位置检测来获取更准确的行列索引
      const target = event.target as HTMLElement;
      const cell = target.closest('.vis-data-sheet__cell') as HTMLElement;

      if (cell) {
        // 从单元格元素直接获取行列索引
        const rowIndex = parseInt(cell.getAttribute('data-row') || '0');
        const colIndex = parseInt(cell.getAttribute('data-col') || '0');

        // 确保索引在有效范围内
        const clampedRowIndex = Math.max(0, Math.min(rowIndex, data.value.length - 1));
        const clampedColIndex = Math.max(0, Math.min(colIndex, columnHeaders.value.length - 1));

        // 确保行和列存在
        ensureRowExists(clampedRowIndex);
        ensureColumnExists(clampedColIndex);

        // 更新选择范围
        const startRow = selectionStart.value.row;
        const startCol = selectionStart.value.col;

        selection.value = {
          startRow: Math.min(startRow, clampedRowIndex),
          startCol: Math.min(startCol, clampedColIndex),
          endRow: Math.max(startRow, clampedRowIndex),
          endCol: Math.max(startCol, clampedColIndex)
        };

        emit('selection-change', selection.value);
        return;
      }

      // 如果无法通过元素检测获取，则使用坐标计算作为备选方案
      const container = document.querySelector('.vis-data-sheet__container') as HTMLElement;
      if (!container) return;

      const scrollContainer = getScrollContainer();
      if (!scrollContainer) return;

      const scrollLeft = scrollContainer.scrollLeft;
      const scrollTop = scrollContainer.scrollTop;
      const containerRect = container.getBoundingClientRect();

      const relativeX = event.clientX - containerRect.left;
      const relativeY = event.clientY - containerRect.top;
      const mouseX = relativeX + scrollLeft;
      const mouseY = relativeY + scrollTop;

      const headerHeight = frozenHeader.value ? 36 : 0;
      const rowHeight = props.cellHeight || 32;
      const contentY = mouseY - headerHeight;
      let rowIndex = Math.max(0, Math.floor(contentY / rowHeight));
      rowIndex = Math.min(rowIndex, data.value.length - 1);

      const cornerWidth = frozenRowHeader.value ? 50 : 0;
      let colIndex = 0;
      let currentX = cornerWidth;

      for (let i = 0; i < columnHeaders.value.length; i++) {
        const colWidth = getColumnWidth(i);
        if (mouseX >= currentX && mouseX < currentX + colWidth) {
          colIndex = i;
          break;
        }
        currentX += colWidth;
      }

      const clampedRowIndex = Math.max(0, rowIndex);
      const clampedColIndex = Math.max(0, Math.min(colIndex, columnHeaders.value.length - 1));

      ensureRowExists(clampedRowIndex);
      ensureColumnExists(clampedColIndex);

      const startRow = selectionStart.value.row;
      const startCol = selectionStart.value.col;

      selection.value = {
        startRow: Math.min(startRow, clampedRowIndex),
        startCol: Math.min(startCol, clampedColIndex),
        endRow: Math.max(startRow, clampedRowIndex),
        endCol: Math.max(startCol, clampedColIndex)
      };

      emit('selection-change', selection.value);
    };

    const stopSelection = () => {
      isSelecting.value = false;
      selectionStart.value = null;

      // 更新多选状态
      if (multiSelection.value.ranges.length > 0) {
        multiSelection.value.activeRange = multiSelection.value.ranges.length;
      }

      document.removeEventListener('mousemove', handleSelectionMove);
      document.removeEventListener('mouseup', stopSelection);
    };

    // 选择相关方法
    const selectCell = (row: number, col: number, event?: MouseEvent) => {
      if (props.readonly) return;

      // 确保行和列存在
      ensureRowExists(row);
      ensureColumnExists(col);

      // 处理多选逻辑
      if (event) {
        if (event.ctrlKey || event.metaKey) {
          // Ctrl/Cmd + 点击：添加单个单元格到多选
          if (hasSelection.value) {
            // 将当前选择添加到多选范围
            const currentRange = getSelectionRange();
            if (currentRange) {
              multiSelection.value.ranges.push(currentRange);
            }
          }

          // 设置新的选择为单个单元格
          selection.value = { startRow: row, startCol: col, endRow: row, endCol: col };
          multiSelection.value.activeRange = multiSelection.value.ranges.length;
        } else if (event.shiftKey && hasSelection.value) {
          // Shift + 点击：范围选择
          const startRow = selection.value.startRow;
          const startCol = selection.value.startCol;

          selection.value = {
            startRow: Math.min(startRow, row),
            startCol: Math.min(startCol, col),
            endRow: Math.max(startRow, row),
            endCol: Math.max(startCol, col)
          };
        } else {
          // 普通点击：清除多选，选择单个单元格
          multiSelection.value.ranges = [];
          multiSelection.value.activeRange = -1;
          selection.value = { startRow: row, startCol: col, endRow: row, endCol: col };
        }
      } else {
        // 程序化选择
        multiSelection.value.ranges = [];
        multiSelection.value.activeRange = -1;
        selection.value = { startRow: row, startCol: col, endRow: row, endCol: col };
      }

      emit('selection-change', selection.value);

      // 确保容器获得焦点，以便接收键盘事件
      nextTick(() => {
        const container = document.querySelector('.vis-data-sheet__container-wrapper') as HTMLElement;
        if (container) {
          container.focus();
        }
      });
    };

    const selectRow = (row: number, event?: MouseEvent) => {
      if (props.readonly) return;

      // 确保行存在
      ensureRowExists(row);

      if (event && (event.ctrlKey || event.metaKey)) {
        // Ctrl/Cmd + 点击行头：添加行选择到多选
        if (hasSelection.value) {
          const currentRange = getSelectionRange();
          if (currentRange) {
            multiSelection.value.ranges.push(currentRange);
          }
        }

        selection.value = { startRow: row, startCol: 0, endRow: row, endCol: columnHeaders.value.length - 1 };
        multiSelection.value.activeRange = multiSelection.value.ranges.length;
      } else {
        // 普通点击：清除多选，选择整行
        multiSelection.value.ranges = [];
        multiSelection.value.activeRange = -1;
        selection.value = { startRow: row, startCol: 0, endRow: row, endCol: columnHeaders.value.length - 1 };
      }

      emit('selection-change', selection.value);

      // 确保容器获得焦点，以便接收键盘事件
      nextTick(() => {
        const container = document.querySelector('.vis-data-sheet__container-wrapper') as HTMLElement;
        if (container) {
          container.focus();
        }
      });
    };

    const selectColumn = (col: number, event?: MouseEvent) => {
      if (props.readonly) return;

      // 确保列存在
      ensureColumnExists(col);

      if (event && (event.ctrlKey || event.metaKey)) {
        // Ctrl/Cmd + 点击列头：添加列选择到多选
        if (hasSelection.value) {
          const currentRange = getSelectionRange();
          if (currentRange) {
            multiSelection.value.ranges.push(currentRange);
          }
        }

        selection.value = { startRow: 0, startCol: col, endRow: data.value.length - 1, endCol: col };
        multiSelection.value.activeRange = multiSelection.value.ranges.length;
      } else {
        // 普通点击：清除多选，选择整列
        multiSelection.value.ranges = [];
        multiSelection.value.activeRange = -1;
        selection.value = { startRow: 0, startCol: col, endRow: data.value.length - 1, endCol: col };
      }

      emit('selection-change', selection.value);

      // 确保容器获得焦点，以便接收键盘事件
      nextTick(() => {
        const container = document.querySelector('.vis-data-sheet__container-wrapper') as HTMLElement;
        if (container) {
          container.focus();
        }
      });
    };

    // #endregion ------------------选择相关方法------------------

    // #region ------------------编辑状态下的键盘事件处理------------------
    /**
     * 编辑状态下的键盘事件处理
     */
    const handleEditKeydown = (event: KeyboardEvent) => {
      if (props.readonly || !isCurrentSheetEditable(currentSheetIndex.value)) return;

      const currentRow = selection.value.startRow;
      const currentCol = selection.value.startCol;

      switch (event.key) {
        case 'Enter':
          event.preventDefault();
          finishEdit();
          // 编辑完成后移动到下一行
          if (event.shiftKey) {
            if (currentRow > 0) selectCell(currentRow - 1, currentCol);
          } else {
            if (currentRow < data.value.length - 1) selectCell(currentRow + 1, currentCol);
          }
          // 确保容器重新获得焦点
          nextTick(() => {
            const container = document.querySelector('.vis-data-sheet__container-wrapper') as HTMLElement;
            if (container) {
              container.focus();
            }
          });
          break;

        case 'Tab':
          event.preventDefault();
          finishEdit();
          // 编辑完成后移动到下一列
          if (event.shiftKey) {
            if (currentCol > 0) selectCell(currentRow, currentCol - 1);
          } else {
            if (currentCol < columnHeaders.value.length - 1) selectCell(currentRow, currentCol + 1);
          }
          // 确保容器重新获得焦点
          nextTick(() => {
            const container = document.querySelector('.vis-data-sheet__container-wrapper') as HTMLElement;
            if (container) {
              container.focus();
            }
          });
          break;

        case 'Escape':
          event.preventDefault();
          cancelEdit();
          // 确保容器重新获得焦点
          nextTick(() => {
            const container = document.querySelector('.vis-data-sheet__container-wrapper') as HTMLElement;
            if (container) {
              container.focus();
            }
          });
          break;
      }
    };

    // 统一的事件处理方法
    const handleUnifiedKeydown = (event: KeyboardEvent) => {
      if (props.readonly || !isCurrentSheetEditable(currentSheetIndex.value)) return;

      // 检查是否在编辑状态，如果是则让编辑输入框处理
      if (editingCell.value) {
        return;
      }

      // 检查是否在重命名状态
      if (renamingSheetIndex.value >= 0) {
        return;
      }

      // 检查是否在编辑列类型状态
      if (editingColumnType.value !== null) {
        return;
      }

      // 优先处理全局快捷键（Ctrl/Cmd + 按键）
      if (event.ctrlKey || event.metaKey) {
        // 复制快捷键 (Ctrl+C / Cmd+C)
        if (event.key === 'c') {
          event.preventDefault();
          copySelected();
          return;
        }

        // 粘贴快捷键 (Ctrl+V / Cmd+V)
        if (event.key === 'v') {
          event.preventDefault();
          pasteData();
          return;
        }

        // 撤销快捷键 (Ctrl+Z / Cmd+Z)
        if (event.key === 'z' && !event.shiftKey) {
          event.preventDefault();
          undo();
          return;
        }

        // 重做快捷键 (Ctrl+Y / Cmd+Y 或 Ctrl+Shift+Z / Cmd+Shift+Z)
        if (event.key === 'y' || (event.key === 'z' && event.shiftKey)) {
          event.preventDefault();
          redo();
          return;
        }
      }

      // 处理单元格导航和编辑相关的按键
      const currentRow = selection.value.startRow;
      const currentCol = selection.value.startCol;

      // 确保有选中的单元格
      if (!hasSelection.value) {
        return;
      }

      switch (event.key) {
        case 'Enter':
          event.preventDefault();
          // 开始编辑当前单元格
          startEdit(currentRow, currentCol);
          break;

        case 'Tab':
          event.preventDefault();
          // 移动到下一个单元格
          if (event.shiftKey) {
            if (currentCol > 0) selectCell(currentRow, currentCol - 1);
          } else {
            if (currentCol < columnHeaders.value.length - 1) selectCell(currentRow, currentCol + 1);
          }
          break;

        case 'Escape':
          event.preventDefault();
          // 取消编辑（如果正在编辑）
          if (editingCell.value) {
            cancelEdit();
          } else if (isFullscreen.value) {
            // ESC键退出全屏
            toggleFullscreen();
          }
          break;

        case 'Backspace':
        case 'Delete':
          event.preventDefault();
          // 删除选中内容
          deleteSelected();
          break;

        case 'ArrowUp':
          event.preventDefault();
          if (currentRow > 0) selectCell(currentRow - 1, currentCol);
          break;

        case 'ArrowDown':
          event.preventDefault();
          if (currentRow < data.value.length - 1) selectCell(currentRow + 1, currentCol);
          break;

        case 'ArrowLeft':
          event.preventDefault();
          if (currentCol > 0) selectCell(currentRow, currentCol - 1);
          break;

        case 'ArrowRight':
          event.preventDefault();
          if (currentCol < columnHeaders.value.length - 1) selectCell(currentRow, currentCol + 1);
          break;

        default: {
          // 处理可输入字符
          const isTypableKey = event.key.length === 1 && !event.ctrlKey && !event.metaKey && !event.altKey;
          if (isTypableKey) {
            event.preventDefault();
            // 直接输入字符时，清空单元格内容并开始编辑
            startEdit(currentRow, currentCol, event.key);
          }
          break;
        }
      }
    };

    const handleMainContainerClick = () => {
      // 确保外层容器重新获得焦点
      const mainContainer = document.querySelector('.vis-data-sheet') as HTMLElement;
      if (mainContainer) {
        mainContainer.focus();
      }

      // 同时确保容器包装器获得焦点，以便接收键盘事件
      ensureContainerWrapperFocus();
    };

    // 容器焦点管理方法
    const ensureContainerWrapperFocus = () => {
      const containerWrapper = document.querySelector('.vis-data-sheet__container-wrapper') as HTMLElement;
      if (containerWrapper) {
        // 确保元素有 tabindex 属性
        if (!containerWrapper.hasAttribute('tabindex')) {
          containerWrapper.setAttribute('tabindex', '0');
        }
        // 尝试获得焦点
        containerWrapper.focus();

        // 如果当前焦点不在容器上，延迟重试
        if (document.activeElement !== containerWrapper) {
          setTimeout(() => {
            containerWrapper.focus();
          }, 50);
        }
      }
    };

    // #endregion ------------------编辑状态下的键盘事件处理------------------

    const onCellFocus = (row: number, col: number) => {
      // 确保行和列存在
      ensureRowExists(row);
      ensureColumnExists(col);

      if (selection.value.startRow !== row || selection.value.startCol !== col) {
        selectCell(row, col);
      }
    };

    // #region ------------------行列操作方法------------------
    // 数据操作方法
    const addRow = () => {
      if (!isCurrentSheetEditable(currentSheetIndex.value)) return;

      const newRow: any[] = [];
      for (let i = 0; i < columnHeaders.value.length; i++) {
        newRow.push('');
      }
      data.value.push(newRow);
      updateDataAndSave(data.value);
    };

    const addColumn = () => {
      if (!isCurrentSheetEditable(currentSheetIndex.value)) return;

      const newColIndex = columnHeaders.value.length;
      columnHeaders.value.push({
        name: getColumnLabel(newColIndex),
        type: FieldType.STRING
      });

      // 更新当前sheet页的列宽设置
      if (currentSheet.value) {
        currentSheet.value.columnWidths[newColIndex] = props.cellWidth;
      }

      for (const row of data.value) {
        row.push('');
      }
      updateDataAndSave(data.value);
    };

    const deleteColumn = (colIndex: number) => {
      if (!isCurrentSheetEditable(currentSheetIndex.value)) return;

      columnHeaders.value.splice(colIndex, 1);
      data.value.forEach((row) => {
        row.splice(colIndex, 1);
      });

      // 更新当前sheet页的列宽设置
      if (currentSheet.value) {
        // 删除对应列的宽度设置
        delete currentSheet.value.columnWidths[colIndex];
        // 重新整理列宽索引
        const newColumnWidths: { [key: number]: number } = {};
        Object.keys(currentSheet.value.columnWidths).forEach((key) => {
          const oldIndex = parseInt(key);
          if (oldIndex > colIndex) {
            newColumnWidths[oldIndex - 1] = currentSheet.value.columnWidths[oldIndex];
          } else if (oldIndex < colIndex) {
            newColumnWidths[oldIndex] = currentSheet.value.columnWidths[oldIndex];
          }
        });
        currentSheet.value.columnWidths = newColumnWidths;
      }

      // 重新计算剩余列的标题，确保列标题连续
      for (let i = 0; i < columnHeaders.value.length; i++) {
        columnHeaders.value[i].name = getColumnLabel(i);
      }

      updateDataAndSave(data.value);
    };

    const deleteRow = (rowIndex: number) => {
      if (!isCurrentSheetEditable(currentSheetIndex.value)) return;

      data.value.splice(rowIndex, 1);
      updateDataAndSave(data.value);
    };

    // #endregion ------------------行列操作方法------------------

    // #region ------------------复制粘贴相关方法------------------
    const deleteSelected = () => {
      if (!isCurrentSheetEditable(currentSheetIndex.value)) return;

      // 删除主选择范围
      const mainRange = getSelectionRange();
      if (mainRange) {
        for (let i = mainRange.minRow; i <= mainRange.maxRow; i++) {
          for (let j = mainRange.minCol; j <= mainRange.maxCol; j++) {
            if (i < data.value.length && j < data.value[i].length) {
              data.value[i][j] = '';
            }
          }
        }
      }

      // 删除多选范围
      multiSelection.value.ranges.forEach((range) => {
        for (let i = range.minRow; i <= range.maxRow; i++) {
          for (let j = range.minCol; j <= range.maxCol; j++) {
            if (i < data.value.length && j < data.value[i].length) {
              data.value[i][j] = '';
            }
          }
        }
      });

      updateDataAndSave(data.value);
    };

    const copySelected = () => {
      // 收集所有选择范围的数据
      const allRanges: SelectionRange[] = [];

      const mainRange = getSelectionRange();
      if (mainRange) {
        allRanges.push(mainRange);
      }

      allRanges.push(...multiSelection.value.ranges);

      if (allRanges.length === 0) return;

      // 获取所有选中列的列头信息，保持顺序
      const selectedColumnHeaders: string[] = [];
      const columnIndexMap = new Map<number, string>();

      allRanges.forEach((range) => {
        for (let j = range.minCol; j <= range.maxCol; j++) {
          if (!columnIndexMap.has(j)) {
            let headerName: string;
            if (j < columnHeaders.value.length) {
              headerName = columnHeaders.value[j].name;
            } else {
              // 如果列头不存在，使用默认的列标签
              headerName = getColumnLabel(j);
            }
            columnIndexMap.set(j, headerName);
          }
        }
      });

      // 按列索引排序，确保列头顺序正确
      const sortedColumnIndices = Array.from(columnIndexMap.keys()).sort((a, b) => a - b);
      sortedColumnIndices.forEach((colIndex) => {
        selectedColumnHeaders.push(columnIndexMap.get(colIndex)!);
      });

      // 收集所有选择范围的数据，按行和列的顺序
      const copyData: any[][] = [];
      allRanges.forEach((range) => {
        for (let i = range.minRow; i <= range.maxRow; i++) {
          const row: any[] = [];
          // 按照排序后的列索引顺序收集数据
          sortedColumnIndices.forEach((colIndex) => {
            if (i < data.value.length && colIndex < data.value[i].length) {
              // 保持原始数据类型，不转换为字符串
              row.push(data.value[i][colIndex]);
            } else {
              row.push('');
            }
          });
          copyData.push(row);
        }
      });

      // 将表格数据转换为标准JSON格式
      const convertToJSON = (tableData: any[][]) => {
        if (tableData.length === 0) return [];

        // 转换为对象数组，保持数据类型和列顺序
        const jsonData = tableData.map((row) => {
          const obj: any = {};
          selectedColumnHeaders.forEach((headerName, index) => {
            const value = row[index];
            // 保持原始数据类型，null/undefined 转换为空字符串
            obj[headerName] = value === null || value === undefined ? '' : value;
          });
          return obj;
        });

        return jsonData;
      };

      const jsonData = convertToJSON(copyData);
      const jsonText = JSON.stringify(jsonData, null, 2);

      navigator.clipboard.writeText(jsonText);
    };

    const pasteTip = ref('');
    const performanceTip = ref('');
    const fullscreenTip = ref('');

    const pasteData = async (externalText?: string | any[] | object) => {
      if (!isCurrentSheetEditable(currentSheetIndex.value)) return;

      try {
        let pasteDataArr: any[][] = [];

        // 统一处理数据的函数
        const processData = (data: any): any[][] => {
          // 辅助函数：处理单元格值
          const processCellValue = (cell: string): any => {
            const trimmedCell = cell.trim();
            if (trimmedCell === '') return '';
            if (trimmedCell === 'null' || trimmedCell === 'undefined') return '';
            if (!isNaN(Number(trimmedCell)) && trimmedCell !== '') {
              return Number(trimmedCell);
            }
            if (trimmedCell === 'true') return true;
            if (trimmedCell === 'false') return false;
            return trimmedCell;
          };

          // 辅助函数：处理文本数据
          const processTextData = (text: string): any[][] => {
            const rows = text.split('\n').filter((row) => row.trim());
            return rows.map((row) => {
              return row.split('\t').map(processCellValue);
            });
          };

          // 辅助函数：处理对象数组格式
          const processObjectArray = (objectArray: any[]): any[][] => {
            if (objectArray.length === 0) return [];

            // 收集所有列标签并保持顺序
            const columnLabelsMap = new Map<string, boolean>();
            const columnLabelsOrder: string[] = [];

            objectArray.forEach((row: any) => {
              if (row && typeof row === 'object') {
                Object.keys(row).forEach((key) => {
                  if (!columnLabelsMap.has(key)) {
                    columnLabelsMap.set(key, true);
                    columnLabelsOrder.push(key);
                  }
                });
              }
            });

            // 为每个数据行创建表格行
            const tableData: any[][] = [];
            objectArray.forEach((row: any) => {
              if (row && typeof row === 'object') {
                const tableRow: any[] = [];
                columnLabelsOrder.forEach((columnLabel) => {
                  const value = row[columnLabel];
                  // 保持原始数据类型，null/undefined 转换为空字符串
                  tableRow.push(value === null || value === undefined ? '' : value);
                });
                tableData.push(tableRow);
              }
            });

            return tableData;
          };

          if (Array.isArray(data)) {
            if (data.length === 0) return [];

            // 检查是否为二维数组
            if (Array.isArray(data[0])) {
              return data as any[][];
            } else if (typeof data[0] === 'object' && data[0] !== null) {
              return processObjectArray(data);
            } else {
              return [data];
            }
          } else if (typeof data === 'object' && data !== null) {
            // 检查是否为嵌套的对象数组格式（如 {sheet1: [{A:1,B:2}, {A:3,B:4}]}）
            const keys = Object.keys(data);
            if (keys.length === 1 && Array.isArray(data[keys[0]])) {
              const arrayData = data[keys[0]];
              if (arrayData.length > 0 && typeof arrayData[0] === 'object' && arrayData[0] !== null) {
                return processObjectArray(arrayData);
              }
            }

            return [Object.values(data)];
          } else if (typeof data === 'string') {
            // 如果是字符串，尝试解析JSON，失败则尝试解析多个单独对象
            try {
              const parsedData = JSON.parse(data);
              return processData(parsedData);
            } catch (jsonError) {
              try {
                // 匹配多个JSON对象，用逗号分隔
                const objectMatches = data.match(/\{[^}]+\}/g);
                if (objectMatches && objectMatches.length > 0) {
                  const objects = objectMatches
                    .map((match) => {
                      try {
                        return JSON.parse(match);
                      } catch (e) {
                        return null;
                      }
                    })
                    .filter((obj) => obj !== null);

                  if (objects.length > 0) {
                    // 使用辅助函数处理对象数组
                    return processObjectArray(objects);
                  }
                }
              } catch (multiObjectError) {
                console.error(multiObjectError);
              }
              // 多个对象解析也失败，按文本处理
              return processTextData(data);
            }
          } else {
            // 其他类型转换为字符串
            return processTextData(String(data));
          }
        };

        // 处理传入的数据或从剪贴板读取
        if (externalText) {
          pasteDataArr = processData(externalText);
        } else {
          // 从剪贴板读取
          let text: string;
          try {
            text = await navigator.clipboard.readText();
          } catch (e) {
            // fireFox下快捷键粘贴会失败
            pasteTip.value = '火狐浏览器下请点击弹出的粘贴按钮完成粘贴（浏览器安全限制）';
            setTimeout(() => {
              pasteTip.value = '';
            }, 2000);
            return;
          }

          if (!text) return;
          pasteDataArr = processData(text);
        }

        if (pasteDataArr.length === 0) return;

        // 收集所有需要粘贴的目标区域
        const targetRanges: SelectionRange[] = [];
        const mainRange = getSelectionRange();
        if (mainRange) targetRanges.push(mainRange);
        targetRanges.push(...multiSelection.value.ranges);
        if (targetRanges.length === 0) {
          const startRow = selection.value.startRow;
          const startCol = selection.value.startCol;
          targetRanges.push({ minRow: startRow, maxRow: startRow, minCol: startCol, maxCol: startCol });
        }
        // 计算需要扩展的最大范围
        let maxRowIndex = 0;
        let maxColIndex = 0;
        targetRanges.forEach((range) => {
          const rangeMaxRow = range.minRow + pasteDataArr.length - 1;
          const rangeMaxCol = range.minCol + Math.max(...pasteDataArr.map((row) => row.length)) - 1;
          maxRowIndex = Math.max(maxRowIndex, rangeMaxRow);
          maxColIndex = Math.max(maxColIndex, rangeMaxCol);
        });
        // 检查是否需要扩展行
        const needMoreRows = maxRowIndex >= data.value.length;
        if (needMoreRows) {
          const rowsToAdd = maxRowIndex - data.value.length + 1;
          for (let i = 0; i < rowsToAdd; i++) {
            const newRow: any[] = [];
            for (let j = 0; j < columnHeaders.value.length; j++) newRow.push('');
            data.value.push(newRow);
          }
        }
        // 检查是否需要扩展列
        const needMoreCols = maxColIndex >= columnHeaders.value.length;
        if (needMoreCols) {
          const colsToAdd = maxColIndex - columnHeaders.value.length + 1;
          for (let i = 0; i < colsToAdd; i++) {
            const newColIndex = columnHeaders.value.length;
            columnHeaders.value.push({
              name: getColumnLabel(newColIndex),
              type: FieldType.STRING
            });

            // 更新当前sheet页的列宽设置
            if (currentSheet.value) {
              currentSheet.value.columnWidths[newColIndex] = props.cellWidth;
            }

            for (const row of data.value) row.push('');
          }
        }
        // 执行粘贴操作到所有目标区域
        targetRanges.forEach((range) => {
          const startRow = range.minRow;
          const startCol = range.minCol;
          for (let i = 0; i < pasteDataArr.length; i++) {
            const rowIndex = startRow + i;
            for (let j = 0; j < pasteDataArr[i].length; j++) {
              const colIndex = startCol + j;
              if (rowIndex < data.value.length && colIndex < data.value[rowIndex].length) {
                data.value[rowIndex][colIndex] = pasteDataArr[i][j];
              }
            }
          }
        });

        // 根据粘贴的数据更新列标题类型
        updateColumnTypesFromData();

        updateDataAndSave(data.value);
        pasteTip.value = '';
      } catch (error) {
        pasteTip.value = '粘贴失败: ' + error;
        console.error('粘贴失败:', error);
      }
    };
    // #endregion ------------------复制粘贴相关方法------------------

    // #region ------------------历史记录管理------------------
    const saveToHistory = () => {
      history.value = history.value.slice(0, historyIndex.value + 1);
      const currentState = JSON.parse(JSON.stringify(data.value));
      history.value.push(currentState);
      historyIndex.value = history.value.length - 1;

      if (history.value.length > 50) {
        history.value.shift();
        historyIndex.value--;
      }
    };

    const undo = () => {
      if (historyIndex.value > 0) {
        historyIndex.value--;
        data.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]));
        emit('update:modelValue', data.value);
      }
    };

    const redo = () => {
      if (historyIndex.value < history.value.length - 1) {
        historyIndex.value++;
        data.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]));
        emit('update:modelValue', data.value);
      }
    };
    // #endregion ------------------历史记录管理------------------

    // #region ------------------初始化数据------------------
    const initializeData = async () => {
      try {
        // 获取静态数据
        const baseUrl = document.baseURI;
        const staticData = await DocumentService.loadStaticData();

        if (staticData && Object.keys(staticData).length > 0) {
          // 处理多个 sheet 的情况
          const sheetKeys = Object.keys(staticData);
          const createdSheets: DataSheet[] = [];

          sheetKeys.forEach((sheetKey) => {
            // 使用唯一的sheet名称
            const uniqueSheetName = generateUniqueSheetName(sheetKey);
            const sheet = createSheet(uniqueSheetName);
            // 解析每个 sheet 的数据
            const parsedData = parseModelValueToTableData({ [sheetKey]: (staticData as any)[sheetKey] });
            sheet.data = parsedData;
            // 设置每个sheet的列头信息
            setSheetColumnHeaders(sheet, { [sheetKey]: (staticData as any)[sheetKey] });
            createdSheets.push(sheet);
          });

          sheets.value = createdSheets;
          currentSheetIndex.value = 0;
        } else {
          // 如果没有静态数据，创建默认sheet
          const initialSheet = createSheet('Sheet1');
          sheets.value = [initialSheet];
          currentSheetIndex.value = 0;
        }
        saveToHistory();
      } catch (error) {
        console.error('加载静态数据失败:', error);
        // 如果加载失败，创建默认sheet
        const initialSheet = createSheet('Sheet1');
        sheets.value = [initialSheet];
        currentSheetIndex.value = 0;
        saveToHistory();
      }
    };

    // 字段类型管理方法
    const startEditColumnType = (colIndex: number) => {
      if (props.readonly || !isCurrentSheetEditable(currentSheetIndex.value)) return;
      editingColumnType.value = colIndex;
    };

    const finishEditColumnType = () => {
      editingColumnType.value = null;
    };

    const changeColumnType = (colIndex: number, newType: FieldType) => {
      if (props.readonly || !isCurrentSheetEditable(currentSheetIndex.value)) return;
      if (colIndex >= 0 && colIndex < columnHeaders.value.length) {
        columnHeaders.value[colIndex].type = newType;
        // 根据类型转换现有数据
        for (let i = 0; i < data.value.length; i++) {
          if (data.value[i] && data.value[i][colIndex] !== undefined) {
            data.value[i][colIndex] = convertValueByType(data.value[i][colIndex], newType);
          }
        }
        updateDataAndSave(data.value);
      }
      finishEditColumnType();
    };

    const convertValueByType = (value: any, targetType: FieldType): any => {
      if (value === null || value === undefined || value === '') {
        return '';
      }

      switch (targetType) {
        case FieldType.DATE:
        case FieldType.STRING:
          return String(value);
        case FieldType.NUMBER: {
          const num = Number(value);
          return isNaN(num) ? '' : num;
        }
        default:
          return value;
      }
    };
    // #endregion ------------------初始化数据------------------

    /**
     * 从 sheetData 中提取列标签并保持原始顺序
     * @param sheetData 表格数据数组
     * @returns 列标签数组，保持原始顺序
     */
    const extractColumnLabelsOrder = (sheetData: any[]): string[] => {
      const columnLabelsMap = new Map<string, boolean>();
      const columnLabelsOrder: string[] = [];

      sheetData.forEach((row: any) => {
        if (row && typeof row === 'object') {
          Object.keys(row).forEach((key) => {
            if (!columnLabelsMap.has(key)) {
              columnLabelsMap.set(key, true);
              columnLabelsOrder.push(key);
            }
          });
        }
      });

      return columnLabelsOrder;
    };

    /**
     * 从 modelValue 中获取 sheet 的数据
     * @param modelValue 格式如 {sheet1:[{A:'2021/10/17',B:'2021/10/31',C:'标准级'}]}
     * @returns sheetData 数组或 null
     */
    const getSheetData = (modelValue: any): any[] | null => {
      const firstSheetKey = Object.keys(modelValue)[0];
      if (!firstSheetKey) return null;

      const sheetData = modelValue[firstSheetKey];
      if (!Array.isArray(sheetData)) return null;

      return sheetData;
    };

    /**
     * 将 props.modelValue 格式解析为表格数据格式
     * @param modelValue 格式如 {sheet1:[{A:'2021/10/17',B:'2021/10/31',C:'标准级'}]}
     * @returns 二维数组格式的表格数据
     */
    const parseModelValueToTableData = (modelValue: any): any[][] => {
      const tableData: any[][] = [];
      const sheetData = getSheetData(modelValue);

      // 如果 sheetData 为空或不存在，根据配置的行数列数创建空行空列
      if (!sheetData || sheetData.length === 0) {
        for (let i = 0; i < props.rows; i++) {
          const row: any[] = [];
          for (let j = 0; j < props.cols; j++) {
            row.push('');
          }
          tableData.push(row);
        }
        return tableData;
      }

      // 获取所有列标签，保持原始顺序
      const columnLabelsOrder = extractColumnLabelsOrder(sheetData);

      // 创建表格数据，保持原始列顺序
      sheetData.forEach((row: any) => {
        if (row && typeof row === 'object') {
          const tableRow = columnLabelsOrder.map((columnLabel) => row[columnLabel] ?? '');
          tableData.push(tableRow);
        }
      });

      return tableData;
    };

    // 为每个sheet设置列头信息的辅助函数
    /**
     * 根据数据内容推断列的数据类型
     * @param sheetData 表格数据
     * @param columnLabel 列标签
     * @returns 推断的数据类型
     */
    const inferColumnType = (sheetData: any[], columnLabel: string): FieldType => {
      let hasNumber = false;
      let hasDate = false;
      let hasString = false;
      let totalValues = 0;

      // 分析该列的所有数据
      sheetData.forEach((row: any) => {
        if (row && typeof row === 'object' && row[columnLabel] !== undefined) {
          const value = row[columnLabel];
          totalValues++;

          if (value === null || value === undefined || value === '') {
            return; // 跳过空值
          }

          // 检查是否为数字
          if (typeof value === 'number' || (!isNaN(Number(value)) && value !== '')) {
            hasNumber = true;
          }
          // 检查是否为日期
          else if (value instanceof Date || (typeof value === 'string' && !isNaN(new Date(value).getTime()))) {
            hasDate = true;
          }
          // 其他情况视为字符串
          else {
            hasString = true;
          }
        }
      });

      // 如果没有任何有效数据，默认为字符串类型
      if (totalValues === 0) {
        return FieldType.STRING;
      }

      if (hasDate) {
        return FieldType.DATE;
      } else if (hasNumber) {
        return FieldType.NUMBER;
      } else {
        return FieldType.STRING;
      }
    };

    /**
     * 根据当前数据更新列标题类型
     */
    const updateColumnTypesFromData = () => {
      if (!data.value || data.value.length === 0) return;

      // 为每一列推断类型
      for (let colIndex = 0; colIndex < columnHeaders.value.length; colIndex++) {
        let hasNumber = false;
        let hasDate = false;
        let hasString = false;
        let totalValues = 0;

        // 分析该列的所有数据
        for (let rowIndex = 0; rowIndex < data.value.length; rowIndex++) {
          const value = data.value[rowIndex][colIndex];
          if (value !== null && value !== undefined && value !== '') {
            totalValues++;

            // 检查是否为数字
            if (typeof value === 'number' || (!isNaN(Number(value)) && value !== '')) {
              hasNumber = true;
            }
            // 检查是否为日期
            else if (value instanceof Date || (typeof value === 'string' && !isNaN(new Date(value).getTime()))) {
              hasDate = true;
            }
            // 其他情况视为字符串
            else {
              hasString = true;
            }
          }
        }

        // 更新列类型
        if (totalValues > 0) {
          if (hasDate) {
            columnHeaders.value[colIndex].type = FieldType.DATE;
          } else if (hasNumber) {
            columnHeaders.value[colIndex].type = FieldType.NUMBER;
          } else {
            columnHeaders.value[colIndex].type = FieldType.STRING;
          }
        }
      }
    };

    const setSheetColumnHeaders = (sheet: DataSheet, modelValue: any) => {
      const sheetData = getSheetData(modelValue);

      // 如果 sheetData 为空或不存在，根据配置的列数创建默认列头
      if (!sheetData || sheetData.length === 0) {
        sheet.columnHeaders = [];
        sheet.columnWidths = {};

        for (let i = 0; i < props.cols; i++) {
          sheet.columnHeaders.push({
            name: getColumnLabel(i),
            type: FieldType.STRING
          });
          sheet.columnWidths[i] = props.cellWidth;
        }
        return;
      }

      // 获取所有列标签，保持原始顺序
      const columnLabelsOrder = extractColumnLabelsOrder(sheetData);

      // 设置列头信息，保持原始顺序
      if (columnLabelsOrder.length >= 0) {
        sheet.columnHeaders = columnLabelsOrder.map((label) => ({
          name: label,
          type: inferColumnType(sheetData, label)
        }));

        // 设置列宽
        sheet.columnWidths = {};
        columnLabelsOrder.forEach((_, index) => {
          sheet.columnWidths[index] = props.cellWidth;
        });
      }
    };

    const getDataTypeIcon = (type: FieldType): string => {
      switch (type) {
        case FieldType.STRING:
          return 'field-string';
        case FieldType.NUMBER:
          return 'field-integer';
        case FieldType.DATE:
          return 'field-date';
        default:
          return 'field-string';
      }
    };

    // 冻结功能方法
    const toggleFrozenHeader = () => {
      if (props.readonly) return;
      frozenHeader.value = !frozenHeader.value;
    };

    const toggleFrozenRowHeader = () => {
      if (props.readonly) return;
      frozenRowHeader.value = !frozenRowHeader.value;
    };

    /**
     * 将数据转换为指定格式
     * @returns {Object} 格式化的数据，如 {sheet1:[{A:'2021/10/17',B:'2021/10/31',C:'标准级'}]}
     */
    const getFormattedData = () => {
      const result: { [key: string]: any[] } = {};

      const newSheets = sheets.value.length > 1 ? sheets.value.slice(1) : [];
      newSheets.forEach((sheet, sheetIndex) => {
        const sheetName = sheet.name || `sheet${sheetIndex + 1}`;
        const formattedRows: any[] = [];

        // 处理每一行数据
        sheet.data.forEach((row) => {
          const formattedRow: { [key: string]: any } = {};

          // 处理每一列数据
          row.forEach((cell, colIndex) => {
            const columnHeader = sheet.columnHeaders[colIndex];
            const columnLabel = columnHeader ? columnHeader.name : getColumnLabel(colIndex);
            formattedRow[columnLabel] = formatCellValue(cell, columnHeader);
          });

          // 只有当行不为空时才添加到结果中
          const hasData = Object.values(formattedRow).some((value) => value !== '');
          if (hasData) {
            formattedRows.push(formattedRow);
          }
        });

        result[sheetName] = formattedRows;
      });

      return result;
    };

    // 监听 props 变化
    watch(
      () => props.modelValue,
      (newValue) => {
        if (newValue && Object.keys(newValue).length > 0) {
          // 从第二个sheet开始处理modelValue数据
          const sheetKeys = Object.keys(newValue);
          const createdSheets: DataSheet[] = [];

          // 保留第一个sheet（静态数据），从第二个开始添加modelValue数据
          if (sheets.value.length > 0) {
            createdSheets.push(sheets.value[0]); // 保留第一个sheet
          }

          sheetKeys.forEach((sheetKey, index) => {
            // 使用唯一的sheet名称
            const uniqueSheetName = generateUniqueSheetName(sheetKey);
            const sheet = createSheet(uniqueSheetName);
            // 解析每个 sheet 的数据
            const parsedData = parseModelValueToTableData({ [sheetKey]: (newValue as any)[sheetKey] });
            sheet.data = parsedData;
            // 设置每个sheet的列头信息
            setSheetColumnHeaders(sheet, { [sheetKey]: (newValue as any)[sheetKey] });
            createdSheets.push(sheet);
          });

          sheets.value = createdSheets;
          currentSheetIndex.value = 0;
        }
      },
      { deep: true }
    );

    // 监听数据变化
    watch(
      () => data.value.length,
      (newLength) => {
        // 性能提示
        if (newLength > 1000) {
          performanceTip.value = `数据量较大 (${newLength} 行)，建议分批处理`;
          setTimeout(() => {
            performanceTip.value = '';
          }, 2000);
        }
      }
    );

    // 监听格式化数据变化
    watch(
      formattedData,
      (newFormattedData) => {
        emit('formatted-data-change', newFormattedData);
      },
      { deep: true }
    );

    // 生命周期
    onMounted(async () => {
      if (props.modelValue && Object.keys(props.modelValue).length > 0) {
        // 先加载静态数据作为第一个sheet
        await initializeData();

        // 然后处理modelValue数据作为后续sheet
        const sheetKeys = Object.keys(props.modelValue);
        const createdSheets: DataSheet[] = [...sheets.value]; // 保留静态数据sheet

        sheetKeys.forEach((sheetKey, index) => {
          // 使用唯一的sheet名称
          const uniqueSheetName = generateUniqueSheetName(sheetKey);
          const sheet = createSheet(uniqueSheetName);
          // 解析每个 sheet 的数据
          const parsedData = parseModelValueToTableData({ [sheetKey]: (props.modelValue as any)[sheetKey] });
          sheet.data = parsedData;
          // 设置每个sheet的列头信息
          setSheetColumnHeaders(sheet, { [sheetKey]: (props.modelValue as any)[sheetKey] });
          createdSheets.push(sheet);
        });

        sheets.value = createdSheets;
        currentSheetIndex.value = 0;
      } else {
        await initializeData();
      }

      // 监听原生paste事件，提升火狐体验
      const container = document.querySelector('.vis-data-sheet__container');
      if (container) {
        container.addEventListener('paste', (event: Event) => {
          if (props.readonly) return;
          const clipboardData = (event as ClipboardEvent).clipboardData || (window as any).clipboardData;
          if (clipboardData) {
            const text = clipboardData.getData('text');
            if (text) {
              pasteData(text);
              event.preventDefault();
            }
          }
        });
      }

      // 初始化
      nextTick(() => {
        // 绑定滚动事件到正确的容器
        const scrollContainer = document.querySelector('.vis-data-sheet__container .scroll');
        if (scrollContainer) {
          scrollContainer.addEventListener('scroll', handleScroll);
          scrollContainer.addEventListener('scroll', handleScrollImmediate);
        }

        // 确保外层容器获得焦点，以便接收全局快捷键
        const mainContainer = document.querySelector('.vis-data-sheet') as HTMLElement;
        if (mainContainer) {
          mainContainer.focus();
        }

        // 确保 container-wrapper 获得焦点，以便接收单元格键盘事件
        ensureContainerWrapperFocus();

        setTimeout(() => {
          ensureContainerWrapperFocus();
        }, 200);
      });
    });

    // 组件卸载时清理事件监听器
    onUnmounted(() => {
      const scrollContainer = document.querySelector('.vis-data-sheet__container .scroll');
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
        scrollContainer.removeEventListener('scroll', handleScrollImmediate);
      }
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    });

    return {
      sheets,
      currentSheetIndex,
      renamingSheetIndex,
      sheetRenameValue,
      data,
      columnHeaders,
      selection,
      formattedData,
      editingCell,
      editValue,
      resizingColumn,
      resizeLine,
      isSelecting,
      hasSelection,
      hasMultiSelection,
      isFullscreen,
      toggleFullscreen,
      canUndo,
      canRedo,
      isCurrentSheetEditable,
      addSheet,
      switchSheet,
      deleteSheet,
      startRenameSheet,
      finishRenameSheet,
      cancelRenameSheet,
      isRenamingSheet,
      getColumnLabel,
      getColumnWidth,
      startResizeColumn,
      formatCellValue,
      isCellSelected,
      isRowSelected,
      isColumnSelected,
      isEditing,
      isFocused,
      selectCell,
      selectRow,
      selectColumn,
      startSelection,
      stopSelection,
      startEdit,
      finishEdit,
      cancelEdit,
      handleEditKeydown,
      handleUnifiedKeydown,
      onCellFocus,
      addRow,
      addColumn,
      deleteSelected,
      copySelected,
      pasteData,
      undo,
      redo,
      pasteTip,
      performanceTip,
      fullscreenTip,
      scrollTop,
      rowHeight,
      ensureRowExists,
      ensureColumnExists,
      handleClose,
      // 全局事件处理
      handleMainContainerClick,
      // 容器焦点管理
      ensureContainerWrapperFocus,
      // 字段类型管理
      editingColumnType,
      startEditColumnType,
      finishEditColumnType,
      changeColumnType,
      getDataTypeIcon,
      FieldType,
      // 数据格式化
      getFormattedData,
      // 冻结功能
      frozenHeader,
      frozenRowHeader,
      getFrozenHeaderStyle,
      getFrozenRowHeaderStyle,
      getFrozenCornerStyle,
      toggleFrozenHeader,
      toggleFrozenRowHeader,
      getFrozenAddRowStyle,
      // 数据解析
      parseModelValueToTableData,
      deleteColumn,
      deleteRow,
      // Sheet名称管理
      generateUniqueSheetName
    };
  }
});
