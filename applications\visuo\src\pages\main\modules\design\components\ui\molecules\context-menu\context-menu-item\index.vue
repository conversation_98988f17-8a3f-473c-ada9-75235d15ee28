<template>
  <q-scroll-area :style="{ maxHeight: '400px', height: '400px', width: '200px' }">
    <q-list dense class="vis-context-menu-item" :class="`depth-${depth}`">
      <template v-for="(group, groupIndex) in menus" :key="groupIndex">
        <template v-for="(action, index) in group" :key="index">
          <!-- 普通菜单项 -->
          <template v-if="typeof action === 'string'">
            <q-item v-if="getAction(action)" clickable v-close-popup :disable="getAction(action)?.disable">
              <!-- <q-item-section avatar v-if="getAction(item)?.icon">
                <q-icon :name="getAction(item)?.icon" size="18px" />
              </q-item-section> -->
              <q-item-section>
                {{ getAction(action)?.title }}
              </q-item-section>
              <q-item-section side>
                <label>
                  <span v-for="(keyboard, index) in getAction(action)?.shortcuts" :key="index">
                    <span v-if="index > 0">&nbsp;+</span>
                    {{ keyboard }}
                  </span>
                </label>
              </q-item-section>
            </q-item>
          </template>

          <!-- 子菜单组 -->
          <template v-else-if="Array.isArray(action)">
            <q-item class="submenu-item">
              <q-item-section>{{ action[0] }}</q-item-section>
              <q-item-section side>
                <q-icon name="keyboard_arrow_right" />
              </q-item-section>

              <!-- 嵌套菜单 -->
              <q-menu anchor="top right" self="top left" :offset="[20, 10]" class="vis-menu">
                <vis-context-menu-item :menus="action[1]" :depth="depth + 1" />
              </q-menu>
            </q-item>
          </template>
        </template>

        <!-- 分组分隔线 -->
        <q-separator v-if="groupIndex < menus.length - 1" :key="`sep-${groupIndex}`" />
      </template>
    </q-list>
  </q-scroll-area>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
