import { ref } from 'vue';
import { defineStore } from '@hetu/core';
import { DesignAction, DesignActionGroup } from '../models';

/**
 * 右键菜单操作集
 * <AUTHOR>
 */
export const useContextMenuStore = defineStore(() => {
  // #region 页面操作
  const pageActions = ref<Record<string, DesignAction>>(
    Object.assign(
      {},
      new DesignAction('group', '添加到页面组', [], 'o_settings', false,'page', false).record,
      new DesignAction('delete', '删除页面', [], 'o_settings', false, 'page', false).record,
      new DesignAction('copy', '复制页面', ['Ctrl', 'C'], 'o_settings', false, 'page', false).record,
      new DesignAction('rename', '重命名', [], 'o_settings', false, 'page', false).record,
      new DesignAction('home', '设为主页', [], 'o_settings', false, 'page', false).record,
      new DesignAction('icon', '添加图标', [], 'o_settings', false, 'page', false).record
    )
  );
  console.log(pageActions.value, 'pageActions');
  // #endregion

  // #region 图层操作
  const layerActions = ref<Record<string, DesignAction>>(
    Object.assign(
      {},
      new DesignAction('rename', '重命名', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('copy', '复制', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('paste', '粘贴', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('paste_replace', '粘贴并替换', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('show_hide', '显示/隐藏图层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('lock_unlock', '锁定/解锁图层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('move_up', '上移一层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('move_down', '下移一层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('move_top', '移到顶层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('move_bottom', '移到底层', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('copy_id', '复制组件ID', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('copy_config', '复制组件配置', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('create_frame', '创建容器', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('create_group', '创建编组', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('cancel_frame_group', '取消容器/编组', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('create_widget', '创建组件', [], 'o_settings', false, 'layer', false).record,
      new DesignAction('edit_widget', '编辑组件', [], 'o_settings', false, 'layer', false).record
    )
  );
  // #endregion


  // #region 构造菜单分组数据
  const getGroupMenu = (sorts: (string | any[])[], actions: Record<string, DesignAction | DesignActionGroup>) => {
    const getGroupMenuInner = (menuSorts: (string | any[])[]): (DesignAction | DesignActionGroup)[] => {
      return menuSorts
        .map((menu) => (Array.isArray(menu) ? getGroupMenuInner(menu) : actions[menu]))
        .filter(Boolean) as (DesignAction | DesignActionGroup)[];
    };
    return getGroupMenuInner(sorts);
  }

  
  // #endregion

  // meta + ctrl键是否按下
  const isMetaCtrl = ref(false);

  return {
    pageActions,
    layerActions,
    getGroupMenu,

    isMetaCtrl
  };
});
