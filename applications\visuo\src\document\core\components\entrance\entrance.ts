import { defineComponent, computed, type PropType } from 'vue';
import { useGraphStyle, useLayout } from '../../hooks';
import { Frame, Graph, GraphType } from '../../models';
import type { Records } from '@hetu/util';

/**
 * 预览/发布入口组件
 */
export default defineComponent({
  name: 'vis-entrance',
  components: {},
  props: {
    graph: {
      type: Object as PropType<Graph>,
      required: true
    },
    parent: {
      type: Object as PropType<Frame>,
      required: false
    }
  },
  setup(props) {
    const { isFlex, isFreeform, isGrid, flexItemStyle, freeformStyle, constraintsStyle, gridItemStyle } = useLayout();
    const { graphClassName, graphComponentName, graphBaseStyle } = useGraphStyle();

    const graph = computed(() => props.graph);
    const parent = computed(() => props.parent);

    const className = computed(() => {
      return graphClassName(graph.value).join(' ');
    });

    const componentName = computed(() => graphComponentName(graph.value));

    const style = computed(() => {
      const getStyle = (graph: Graph, parent?: Frame) => {
        let positionStyle: Records<string | number> = {};

        // 说明frame是容器下的子元素
        if (parent) {
          // 父级是自由布局
          if (isFreeform(parent)) {
            // 处理设置约束时的位置
            if (graph.constraints) {
              positionStyle = constraintsStyle(graph, parent);
            }
          }
          // 父级是flex布局
          else if (isFlex(parent)) {
            if (graph.ignoreAutoLayout) {
              positionStyle = constraintsStyle(graph);
            } else {
              positionStyle = flexItemStyle(graph, parent);
            }
          }
          // 父级是grid布局
          else if (isGrid(parent)) {
            if (graph.ignoreAutoLayout && graph.constraints) {
              positionStyle = constraintsStyle(graph, parent);
            } else {
              positionStyle = gridItemStyle(graph, parent);
            }
          }
        } else {
          positionStyle = freeformStyle(graph);
        }

        // 基础样式
        const baseStyle = graphBaseStyle(graph);

        return { ...positionStyle, ...baseStyle };
      };
      return getStyle(graph.value, parent.value);
    });

    return {
      style,
      className,
      componentName,
      GraphType
    };
  }
});
