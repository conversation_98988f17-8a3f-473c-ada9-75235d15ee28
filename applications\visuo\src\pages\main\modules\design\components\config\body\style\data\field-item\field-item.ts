import type { <PERSON>set<PERSON>ield } from '@hetu/metadata-shared';
import { defineComponent, ref, type PropType, computed, nextTick } from 'vue';
import VisFieldList from '../field-list/field-list.vue';
import VisFieldConfig from '../field-config/field-config.vue';
import { AxisField, WidgetBlock, Block, Graph, useDocumentStore } from '@vis/document-core';
import draggable from 'vuedraggable';
import { useDesignStore } from '../../../../../../stores';
import { useDataset } from '../../../../../../hooks';

/**
 * 字段项组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-field-item',
  components: {
    draggable,
    VisFieldList,
    VisFieldConfig
  },
  props: {
    fieldMapping: {
      type: Object as PropType<{ datasetName: string; datasetField: DatasetField[] }>,
      default: () => ({})
    },
    mapKey: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { active } = useDesignStore();

    const activeGraph = computed(() => active.value.graph as Graph);

    const docStore = useDocumentStore();

    // 获取当前选中的组件
    const activeBlock = computed(() => {
      const blockId = (activeGraph.value as Block)?.decoration;
      if (!blockId) return null;
      return (docStore.document.value.blocks.find((b) => b.id === blockId) as WidgetBlock) || null;
    });

    const dataMapping = computed(() => activeBlock.value?.dataMapping || {});

    //当前选中的字段id
    const activeIds = computed(() => {
      return dataMapping.value[props.mapKey]?.map((field: AxisField) => field.id || field.fieldName) || [];
    });

    // 控制字段列表展开状态
    const openStates = ref<Record<string, boolean>>({});

    // 控制字段配置菜单显示状态
    const configStates = ref<Record<string, boolean>>({});

    // 获取或设置指定的展开状态
    const getOpenState = (index: number) => openStates.value[`${props.mapKey}-${index}`] || false;
    const toggleOpenState = (index: number, type: 'open' | 'close') => {
      if (index !== -1) {
        openStates.value[`${props.mapKey}-${index}`] = type === 'open' ? true : false;
      }
    };

    // 获取或设置字段配置菜单的显示状态
    const getConfigState = (index: number) => configStates.value[`${props.mapKey}-${index}`] || false;
    const toggleConfigState = (index: number, isVisible: boolean) => {
      configStates.value[`${props.mapKey}-${index}`] = isVisible;
    };

    const getItemKey = (item: AxisField) => {
      return item.fieldName || item.id || Math.random().toString();
    };

    // 删除字段
    const removeField = (index: number) => {
      const key = props.mapKey as string;
      const fields = dataMapping.value[key];
      if (fields && fields.length > index) {
        fields.splice(index, 1);
      }
      dataMapping.value[key] = fields;
    };

    // 添加字段
    const addField = (fields: DatasetField[]) => {
      dataMapping.value[props.mapKey] = fields;
    };

    // 单选逻辑
    const selectField = (field: DatasetField[], index: string) => {
      const oldIndex = dataMapping.value[props.mapKey]?.findIndex((item: AxisField) => item.id === field[0].id);
      dataMapping.value[props.mapKey][Number(index)] = field[0];
      if (oldIndex !== -1) {
        dataMapping.value[props.mapKey].splice(oldIndex, 1);
      }
    };

    const { getDataTypeIcon } = useDataset();

    // 处理字段前的icon
    const getFieldIcon = (field: AxisField) => {
      const sorts = dataMapping.value['sorts'];
      if (sorts && sorts.length > 0) {
        const sortField = sorts.find((sort: AxisField) => sort.id === field.id);
        if (sortField) {
          return sortField.sortDir === 'asc' ? 'asc' : 'desc';
        }
      }
      return 'field-' + getDataTypeIcon(field.fieldDatatype);
    };

    // 处理过滤字段
    const handleFilter = (index: number) => {
      // 检查原始字段是否存在
      const sourceField = dataMapping?.value[props.mapKey]?.[index];
      if (!sourceField) {
        return;
      }

      if (dataMapping.value['filters'] && dataMapping.value['filters'].length > 0) {
        const filterIndex = dataMapping.value['filters'].findIndex((filter: AxisField) => filter.id === sourceField.id);
        if (filterIndex !== -1) {
          // 更新已存在的筛选字段，保持筛选相关属性
          const existingFilter = dataMapping.value['filters'][filterIndex];
          dataMapping.value['filters'][filterIndex] = {
            ...sourceField,
            // 保持原有的筛选属性
            filterExpression: existingFilter.filterExpression,
            filterRule: existingFilter.filterRule,
            includeNull: existingFilter.includeNull
          };
        } else {
          dataMapping.value['filters'].push({
            ...sourceField
          });
        }
      } else {
        dataMapping.value['filters'] = [
          {
            ...sourceField
          }
        ];
      }
    };

    // 处理排序字段
    const handleSort = (index: number, order: 'asc' | 'desc') => {
      // 检查原始字段是否存在
      const sourceField = dataMapping?.value[props.mapKey]?.[index];
      if (!sourceField) {
        return;
      }

      if (dataMapping.value['sorts'] && dataMapping.value['sorts'].length > 0) {
        const sortIndex = dataMapping.value['sorts'].findIndex((sort: AxisField) => sort.id === sourceField.id);
        if (sortIndex !== -1) {
          // 更新已存在的排序字段，保持排序相关属性
          const existingSort = dataMapping.value['sorts'][sortIndex];
          dataMapping.value['sorts'][sortIndex] = {
            ...sourceField,
            // 保持原有的排序属性
            sortDir: order
          };
        } else {
          // 添加新的排序字段
          dataMapping.value['sorts'].push({
            ...sourceField,
            sortDir: order
          });
        }
      } else {
        // 初始化排序数组
        dataMapping.value['sorts'] = [
          {
            ...sourceField,
            sortDir: order
          }
        ];
      }
    };

    return {
      activeIds,
      dataMapping,
      getOpenState,
      toggleOpenState,
      getConfigState,
      toggleConfigState,
      getItemKey,
      removeField,
      addField,
      selectField,
      getFieldIcon,
      handleFilter,
      handleSort
    };
  }
});
