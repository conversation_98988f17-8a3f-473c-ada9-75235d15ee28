import { defineStore } from '@hetu/core';
import {
  WidgetConfigFactory,
  WidgetConfigOptionFactory,
  WidgetConfigInteractFactory,
  WidgetConfigDataFactory
} from '../factory';

/**
 * 组件store
 */
export const useWidgetConfigStore = defineStore(() => {
  const widgetConfigs = new WidgetConfigFactory();

  const configs = Object.values(widgetConfigs.configs);

  const interactConfigs = new WidgetConfigInteractFactory();

  const optionConfigs = new WidgetConfigOptionFactory();

  const dataConfigs = new WidgetConfigDataFactory();

  return {
    /** 组件基础配置 */
    widgetConfigs,
    /** 组件交互配置 */
    interactConfigs,
    /** 组件配置 */
    configs,
    /** 组件属性配置 */
    optionConfigs,
    /** 组件数据配置 */
    dataConfigs
  };
});
