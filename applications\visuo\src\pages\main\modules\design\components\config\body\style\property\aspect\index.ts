import { Graph } from '@vis/document-core';
import { computed, defineComponent, nextTick, ref, watch, type PropType, type Ref } from 'vue';

/**
 * 外观
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-aspect',
  props: {
    option: {
      type: Object as PropType<Graph>,
      required: true
    }
  },
  setup(props) {
    const graph = computed(() => props.option as Graph);

    //#region 圆角
    /** 圆角 */
    const radius: Ref<number | string> = ref(graph.value.radius[0]);

    const radiusChange = () => {
      if (typeof radius.value === 'number') {
        graph.value.radius = [radius.value, radius.value, radius.value, radius.value];
      }
    };

    const showRadius = ref(false);

    const flag = computed(() => !graph.value.radius.every((item) => item === graph.value.radius[0]));

    watch(
      () => graph.value.id,
      () => {
        radius.value = graph.value.radius[0];
        showRadius.value = flag.value;
      },
      {
        immediate: true
      }
    );

    watch(
      () => flag.value,
      () => {
        radius.value = flag.value ? 'Mixed' : graph.value.radius[0];
      }
    );

    //#endregion

    return {
      graph,

      radius,
      radiusChange,
      showRadius
    };
  }
});
