import {
  DirectionType,
  GRAPH_SIZE_MIN,
  TabResizeType,
  SelectMode,
  type TabOptions,
  IconOption,
  TabStyle,
  FillPaints,
  Stroke,
  Effects,
  TabItemStyle,
  StrokeType,
  IconPosition,
  iconPositionOptions
} from '@vis/document-core';
import { computed, defineComponent, onMounted, ref, type PropType } from 'vue';
import VisAspect from '../../property/aspect/index.vue';
import VisConfigTabStatus from './status/status.vue';

/**
 * <AUTHOR>
 * 选项卡组件属性面板
 */
export default defineComponent({
  name: 'vis-config-tab-option',
  components: {
    VisAspect,
    VisConfigTabStatus
  },
  props: {
    options: {
      type: Object as PropType<TabOptions>,
      required: true
    }
  },
  setup(props) {
    const selectMode = ref(SelectMode.Single);

    const hoverItem = ref('text');

    const activeItem = ref('text');
    const tabOptions = computed(() => props.options);
    const tabStyle = computed(() => props.options.style);

    const popupShow = ref(false);

    const selectOptions = [
      {
        value: SelectMode.Single,
        label: '单选模式'
      },
      {
        value: SelectMode.Multiple,
        label: '多选模式'
      }
    ];

    const overflowOptions = [
      {
        value: 0,
        label: '溢出'
      },
      {
        value: 1,
        label: '省略号'
      },
      {
        value: 2,
        label: '换行'
      },
      {
        value: 3,
        label: '跑马灯'
      }
    ];

    const textDirectionOptions = [
      {
        value: DirectionType.Horizontal,
        label: '',
        icon: `hticon-vis-layout-${DirectionType.Horizontal}`,
        tip: '水平'
      },
      {
        value: DirectionType.Vertical,
        label: '',
        icon: `hticon-vis-layout-${DirectionType.Vertical}`,
        tip: '垂直'
      }
    ];

    const lineOptions = [
      { label: '实线', value: StrokeType.Solid, style: 'solid' },
      { label: '虚线', value: StrokeType.Dashed, style: 'dashed' },
      { label: '点线', value: StrokeType.Dotted, style: 'dotted' }
    ];

    const directionOptions = [
      {
        value: DirectionType.Horizontal,
        label: '',
        icon: `hticon-vis-layout-${DirectionType.Horizontal}`,
        tip: '水平'
      },
      { value: DirectionType.Vertical, label: '', icon: `hticon-vis-layout-${DirectionType.Vertical}`, tip: '垂直' },
      { value: DirectionType.Grid, label: '', icon: `hticon-vis-layout-${DirectionType.Grid}`, tip: '网格' }
    ];

    /**
     * 修改选择模式
     * @param val
     */
    const onChangeSelectMode = (val: SelectMode) => {
      tabOptions.value.multiple = val === SelectMode.Multiple ? true : false;
    };

    //#region  布局
    const popupRef = ref();
    const showMenuWidth = ref(false);
    const showMenuHeight = ref(false);

    const onShowPopup = (e: Event) => {
      e.stopPropagation();
      popupRef.value?.handleShow(e);
    };

    const onChangeResize = (type: 'resizeX' | 'resizeY', resize: TabResizeType) => {
      tabOptions.value[type] = resize;
    };

    const hoverGrid = ref([0, 0]);
    const onMouseMovePicker = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.className.indexOf('hand') !== -1) {
        const row = target.getAttribute('data-row');
        const col = target.getAttribute('data-col');
        hoverGrid.value = [Number(row), Number(col)];
      }
    };

    const onMouseLeavePicker = () => {
      hoverGrid.value = [0, 0];
    };

    const onClickGrid = (row: number, col: number) => {
      tabOptions.value.layout.row = row;
      tabOptions.value.layout.column = col;
    };

    /**
     * 行距显示条件
     */
    const getShowLineGutter = computed(
      () =>
        tabOptions.value.layout.direction === DirectionType.Vertical ||
        (tabOptions.value.layout.flowWrap &&
          tabOptions.value.layout.direction === DirectionType.Horizontal &&
          tabOptions.value.resizeX === TabResizeType.Fixed)
    );
    //#endregion 布局

    //#region --------- 圆角 ---------------
    const radiusTop = ref(props.options.style.radius[0]);
    const radiusBottom = ref(props.options.style.radius[2]);

    const showRadius = ref(false);

    const radiusChange = (val: number, position: string) => {
      if (position === 'top') {
        tabOptions.value.style.radius[0] = val;
        tabOptions.value.style.radius[1] = val;
      } else {
        tabOptions.value.style.radius[2] = val;
        tabOptions.value.style.radius[3] = val;
      }
    };

    //#endregion --------- 圆角 ---------------

    //#region ------------图标 ------------
    const toggleIcon = () => {
      if (tabOptions.value.icon) {
        tabOptions.value.icon = undefined;
      } else {
        tabOptions.value.icon = new IconOption();
      }
    };
    //#endregion ----------图标 -----------

    //#region-------------- 填充 描边 特效 --------------

    // const addStyle = () => {
    //   tabOptions.value.style.background = new FillPaints();
    // };

    // const deleteStyle = () => {
    //   tabOptions.value.style.background = undefined;
    // };

    // const toggleStyle = (key: 'shadow' | 'border') => {
    //   if (key === 'shadow') {
    //     tabOptions.value.style.shadow = tabOptions.value.style.shadow ? undefined : new Effects();
    //   }
    //   if (key === 'border') {
    //     tabOptions.value.style.border = tabOptions.value.style.border ? undefined : new Stroke();
    //   }
    // };

    //#endregion-------------- 填充 描边 特效 ---------------

    //#region ---------------- 状态 ----------------------

    const addStatus = (status: 'hover' | 'active') => {
      tabStyle.value[status] = new TabItemStyle();
    };

    const deleteStatus = (status: 'hover' | 'active') => {
      if (tabStyle.value[status]) {
        tabStyle.value[status] = undefined;
      }
    };

    //#endregion ---------------- 状态 ----------------------

    onMounted(() => {
      selectMode.value = tabOptions.value.multiple ? SelectMode.Multiple : SelectMode.Single;
    });

    return {
      selectMode,
      tabOptions,
      tabStyle,
      selectOptions,
      overflowOptions,
      textDirectionOptions,
      hoverItem,
      activeItem,
      TabResizeType,
      showMenuWidth,
      showMenuHeight,
      radiusTop,
      radiusBottom,
      showRadius,
      lineOptions,
      directionOptions,
      DirectionType,
      hoverGrid,
      getShowLineGutter,
      iconPositionOptions,
      popupRef,
      popupShow,
      onChangeSelectMode,
      onChangeResize,
      radiusChange,
      toggleIcon,
      // addStyle,
      // deleteStyle,
      // toggleStyle,
      addStatus,
      deleteStatus,
      onMouseMovePicker,
      onMouseLeavePicker,
      onClickGrid,
      onShowPopup
    };
  }
});
