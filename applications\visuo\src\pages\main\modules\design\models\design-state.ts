import type { Records } from '@hetu/util';
import { Frame, Graph, Page } from '@vis/document-core';

export class DesignActiveState {
  /** 当前选中的页面 */
  page: Page = new Page();
  /** 当前选中的容器: 当前选中图形的父Frame */
  frame?: Frame;
  /** 当前选中的图形 */
  graphs: Graph[] = [];
  /** 当前选中的图形id */
  graphIds: string[] = [];
  /** 当前选中的图形 */
  get graph() {
    return this.graphs.length ? this.graphs[0] : undefined;
  }
}

/**
 * 容器状态
 */
export class FrameState {
  /** grid布局时：网格项大小和位置 */
  gridItemRect: Records<{ w: number; h: number; x: number; y: number }> = {};
}

/**
 * 标尺状态
 */
export class RulerState {
  /** 缩放范围 */
  zoomRange: number[] = [0.1, 2];
  /** 当前缩放比例 */
  zoom: number = 1;
  /** 标尺选中的范围 */
  selectedRangesH: number[][] = [];
  selectedRangesV: number[][] = [];
  /** 初始参考线基准位置 */
  defaultGuidesPos: number = 0;
  /** 初始滚动位置 */
  defaultScrollPos: number = 0;
}

/**
 * 画布状态
 */
export class CanvasState {
  /** 是否显示用于添加组件的容器 */
  dropbox: boolean = false;
  /** 当前正在旋转图形 */
  isRotate: boolean = false;
  /** 当前正在拖拽的图形id，用于在flex布局中，正在拖拽的图形脱离flex布局 */
  dragging: string[] = [];
  /** 当前正在调整大小的图形 id*/
  resizeing: string[] = [];
  /** 当前可放置的容器 1.从左侧面板拖拽组件时，记录组件放置在哪个容器内 */
  frame: Frame | undefined;
  /** activeFrame为grid布局时，记录拖拽过程中图形放置的格子位置 [row, col]*/
  gridRowCol: number[] | undefined;
  /** 存储平级的容器,用于计算当前鼠标在哪个容器内 */
  flattenFrames: Frame[] = [];
}
