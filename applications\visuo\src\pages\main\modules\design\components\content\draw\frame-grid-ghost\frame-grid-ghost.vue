<template>
  <div class="vis-frame-grid-ghost" v-if="isGhost && isMoveable" :style="style">
    <div class="left" :style="ghostLeftStyle">
      <div class="hand" :style="rowsStyle">
        <div class="ghost item" v-for="i in gridSize[0]" :key="i">
          <div
            class="tip"
            :class="{ '!visible': i === hoverRowCol[0] || rowsSize[i - 1].isEdit }"
            @click="onClickTip(i - 1, 'row')"
          >
            <span v-if="!rowsSize[i - 1].isEdit" class="text">{{ rowsSize[i - 1].value }}</span>
            <q-input
              v-else
              ref="inputRef"
              v-model="rowsSize[i - 1].value"
              @blur="onBlurSize(i - 1, 'row')"
              @keyup.enter="onBlurSize(i - 1, 'row')"
              class="vis-input rounded-borders flex-1 px-0 pr-0 vis-field--mini"
              type="text"
              borderless
              dense
            />
          </div>
        </div>
      </div>
    </div>
    <div class="top" :style="ghostTopStyle">
      <div class="hand" :style="colsStyle">
        <div class="ghost item" v-for="i in gridSize[1]" :key="i">
          <div
            class="tip"
            :class="{ '!visible': i === hoverRowCol[1] || columnsSize[i - 1].isEdit }"
            @click="onClickTip(i - 1, 'col')"
          >
            <span v-if="!columnsSize[i - 1].isEdit" class="text">{{ columnsSize[i - 1].value }}</span>
            <q-input
              v-else
              ref="inputRef"
              v-model="columnsSize[i - 1].value"
              @blur="onBlurSize(i - 1, 'col')"
              @keyup.enter="onBlurSize(i - 1, 'col')"
              class="vis-input rounded-borders flex-1 px-0 pr-0 vis-field--mini"
              type="text"
              borderless
              dense
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./frame-grid-ghost.ts"></script>
<style lang="scss" src="./frame-grid-ghost.scss"></style>
